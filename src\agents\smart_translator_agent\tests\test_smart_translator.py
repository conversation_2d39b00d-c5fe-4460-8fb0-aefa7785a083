"""
Tests for Smart Translator Agent

Comprehensive tests for the enhanced translation system.
"""

import unittest
import tempfile
import os
import pandas as pd
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# Import the classes to test
from src.agents.smart_translator_agent.smart_translator import SmartTranslatorAgent, SmartTranslatorAnswer
from src.agents.smart_translator_agent.models.document_processor import ExcelProcessor, TextProcessor, DocumentProcessorFactory
from src.agents.smart_translator_agent.models.translation_service import AzureTranslationService, TranslationRequest
from src.agents.smart_translator_agent.models.progress_tracker import ProgressTracker, TaskStatus
from src.agents.smart_translator_agent.models.translation_memory import TranslationMemory
from src.backend.contracts.chat_data import BotType


class TestSmartTranslatorAgent(unittest.TestCase):
    """Test cases for Smart Translator Agent"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.agent = SmartTranslatorAgent(
            bot_name=BotType.SMART_TRANSLATOR_BOT.name,
            user_id="test_user_123"
        )
        
        # Create temporary directory for test files
        self.temp_dir = tempfile.mkdtemp()
        self.agent.upload_dir = Path(self.temp_dir)
    
    def tearDown(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_agent_initialization(self):
        """Test agent initialization"""
        self.assertEqual(self.agent.user_id, "test_user_123")
        self.assertIsNotNone(self.agent.translation_service)
        self.assertIsNotNone(self.agent.document_processor_factory)
        self.assertTrue(self.agent.upload_dir.exists())
    
    def test_get_file_path(self):
        """Test file path generation"""
        # Test with filename
        path = self.agent.get_file_path("test.xlsx")
        self.assertTrue(path.endswith("test_user_123_test.xlsx"))
        
        # Test without filename (default)
        path = self.agent.get_file_path()
        self.assertTrue(path.endswith("data_test_user_123.xlsx"))
    
    def test_get_supported_formats(self):
        """Test getting supported file formats"""
        formats = self.agent.get_supported_formats()
        self.assertIsInstance(formats, list)
        self.assertIn('.xlsx', formats)
        self.assertIn('.txt', formats)
    
    def test_get_supported_languages(self):
        """Test getting supported languages"""
        with patch.object(self.agent.translation_service, 'get_supported_languages') as mock_langs:
            mock_langs.return_value = ['en', 'es', 'fr']
            languages = self.agent.get_supported_languages()
            self.assertEqual(languages, ['en', 'es', 'fr'])
    
    @patch('src.agents.smart_translator_agent.smart_translator.progress_tracker')
    @patch('src.agents.smart_translator_agent.smart_translator.translation_history')
    def test_ask_method_file_not_found(self, mock_history, mock_tracker):
        """Test ask method with non-existent file"""
        # Test with non-existent file
        answer = self.agent.ask(
            "Translate to Spanish",
            file_path="/non/existent/file.xlsx"
        )
        
        self.assertIsInstance(answer, SmartTranslatorAnswer)
        self.assertEqual(answer.status, "failed")
        self.assertIn("File not found", answer.info)
    
    def test_create_test_excel_file(self):
        """Helper method to create test Excel file"""
        test_file = os.path.join(self.temp_dir, "test.xlsx")
        
        # Create test data
        data = {
            'English': ['Hello', 'World', 'Test'],
            'Numbers': [1, 2, 3],
            'Description': ['This is a test', 'Another test', 'Final test']
        }
        df = pd.DataFrame(data)
        df.to_excel(test_file, index=False)
        
        return test_file
    
    @patch('src.agents.smart_translator_agent.smart_translator.progress_tracker')
    @patch('src.agents.smart_translator_agent.smart_translator.translation_history')
    @patch('src.agents.smart_translator_agent.smart_translator.asyncio')
    def test_ask_method_success(self, mock_asyncio, mock_history, mock_tracker):
        """Test successful translation request"""
        # Create test Excel file
        test_file = self.create_test_excel_file()
        
        # Mock the async translation
        mock_asyncio.run.return_value = {
            'Sheet1:English:0': 'Hola',
            'Sheet1:English:1': 'Mundo',
            'Sheet1:English:2': 'Prueba'
        }
        
        # Mock progress tracker
        mock_tracker.create_task.return_value = "task_123"
        mock_tracker.add_step.return_value = "step_123"
        
        # Mock translation history
        mock_history.add_entry.return_value = 1
        
        answer = self.agent.ask(
            "Translate to Spanish",
            file_path=test_file,
            target_language="es",
            source_language="en"
        )
        
        self.assertIsInstance(answer, SmartTranslatorAnswer)
        # Note: This test might fail due to actual translation service calls
        # In a real test environment, we'd mock the translation service


class TestDocumentProcessor(unittest.TestCase):
    """Test cases for Document Processor"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_excel_processor_can_process(self):
        """Test Excel processor file type detection"""
        processor = ExcelProcessor()
        self.assertTrue(processor.can_process("test.xlsx"))
        self.assertFalse(processor.can_process("test.txt"))
    
    def test_text_processor_can_process(self):
        """Test Text processor file type detection"""
        processor = TextProcessor()
        self.assertTrue(processor.can_process("test.txt"))
        self.assertTrue(processor.can_process("test.csv"))
        self.assertFalse(processor.can_process("test.xlsx"))
    
    def test_document_processor_factory(self):
        """Test Document Processor Factory"""
        factory = DocumentProcessorFactory()
        
        # Test Excel file
        processor = factory.get_processor("test.xlsx")
        self.assertIsInstance(processor, ExcelProcessor)
        
        # Test text file
        processor = factory.get_processor("test.txt")
        self.assertIsInstance(processor, TextProcessor)
        
        # Test unsupported file
        processor = factory.get_processor("test.pdf")
        self.assertIsNone(processor)
    
    def test_excel_processor_extract_content(self):
        """Test Excel content extraction"""
        # Create test Excel file
        test_file = os.path.join(self.temp_dir, "test.xlsx")
        data = {
            'Column1': ['Hello', 'World'],
            'Column2': ['Test', 'Data']
        }
        df = pd.DataFrame(data)
        df.to_excel(test_file, index=False)
        
        processor = ExcelProcessor()
        structure = processor.extract_content(test_file)
        
        self.assertIsNotNone(structure)
        self.assertTrue(len(structure.translatable_content) > 0)
        
        # Check that content was extracted
        content_texts = [content.content for content in structure.translatable_content]
        self.assertIn('Hello', content_texts)
        self.assertIn('World', content_texts)
    
    def test_text_processor_extract_content(self):
        """Test text content extraction"""
        # Create test text file
        test_file = os.path.join(self.temp_dir, "test.txt")
        test_content = "This is a test file for translation."
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        processor = TextProcessor()
        structure = processor.extract_content(test_file)
        
        self.assertIsNotNone(structure)
        self.assertEqual(len(structure.translatable_content), 1)
        self.assertEqual(structure.translatable_content[0].content, test_content)


class TestTranslationService(unittest.TestCase):
    """Test cases for Translation Service"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Mock the config to avoid actual API calls
        self.mock_config = Mock()
        self.mock_config.translator_azure_ai_api_translator_endpoint = "https://test.api.com"
        self.mock_config.translator_azure_ai_api_translator_key = "test_key"
    
    @patch('src.agents.smart_translator_agent.models.translation_service.EproExcelLaConfig')
    def test_azure_translation_service_init(self, mock_config_class):
        """Test Azure Translation Service initialization"""
        mock_config_class.return_value = self.mock_config
        
        service = AzureTranslationService()
        self.assertIsNotNone(service.endpoint)
        self.assertIsNotNone(service.key)
        self.assertIsNotNone(service.headers)
    
    @patch('src.agents.smart_translator_agent.models.translation_service.requests')
    @patch('src.agents.smart_translator_agent.models.translation_service.EproExcelLaConfig')
    def test_translate_single_success(self, mock_config_class, mock_requests):
        """Test single translation success"""
        mock_config_class.return_value = self.mock_config
        
        # Mock successful API response
        mock_response = Mock()
        mock_response.json.return_value = [{
            'translations': [{'text': 'Hola'}],
            'detectedLanguage': {'language': 'en', 'score': 0.95}
        }]
        mock_response.raise_for_status.return_value = None
        mock_requests.post.return_value = mock_response
        
        service = AzureTranslationService()
        request = TranslationRequest(
            text="Hello",
            source_language="en",
            target_language="es",
            location="test_location"
        )
        
        result = service.translate_single(request)
        
        self.assertEqual(result.translated_text, "Hola")
        self.assertEqual(result.source_language, "en")
        self.assertEqual(result.target_language, "es")
        self.assertEqual(result.confidence, 0.95)


class TestProgressTracker(unittest.TestCase):
    """Test cases for Progress Tracker"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.tracker = ProgressTracker()
    
    def test_create_task(self):
        """Test task creation"""
        task_id = self.tracker.create_task("user123", "test.xlsx", 100)
        self.assertIsNotNone(task_id)
        
        # Check task exists
        task = self.tracker.get_task_progress(task_id)
        self.assertIsNotNone(task)
        self.assertEqual(task.user_id, "user123")
        self.assertEqual(task.file_name, "test.xlsx")
        self.assertEqual(task.total_items, 100)
    
    def test_add_step(self):
        """Test adding steps to task"""
        task_id = self.tracker.create_task("user123", "test.xlsx", 100)
        step_id = self.tracker.add_step(task_id, "extract", "Extracting content")
        
        self.assertIsNotNone(step_id)
        
        task = self.tracker.get_task_progress(task_id)
        self.assertEqual(len(task.steps), 1)
        self.assertEqual(task.steps[0].name, "extract")
    
    def test_update_task_progress(self):
        """Test updating task progress"""
        task_id = self.tracker.create_task("user123", "test.xlsx", 100)
        
        self.tracker.update_task_progress(task_id, processed_items=50)
        
        task = self.tracker.get_task_progress(task_id)
        self.assertEqual(task.processed_items, 50)
        self.assertEqual(task.overall_progress, 0.5)
    
    def test_cancel_task(self):
        """Test task cancellation"""
        task_id = self.tracker.create_task("user123", "test.xlsx", 100)
        step_id = self.tracker.add_step(task_id, "extract", "Extracting content")
        
        self.tracker.cancel_task(task_id)
        
        task = self.tracker.get_task_progress(task_id)
        self.assertEqual(task.status, TaskStatus.CANCELLED)
        self.assertEqual(task.steps[0].status, TaskStatus.CANCELLED)


class TestTranslationMemory(unittest.TestCase):
    """Test cases for Translation Memory"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Use in-memory database for testing
        self.memory = TranslationMemory(":memory:")
    
    def test_store_and_retrieve_translation(self):
        """Test storing and retrieving translations"""
        # Store a translation
        self.memory.store_translation(
            source_text="Hello",
            target_text="Hola",
            source_language="en",
            target_language="es",
            provider="azure",
            confidence=0.95
        )
        
        # Retrieve the translation
        entry = self.memory.get_translation("Hello", "en", "es")
        
        self.assertIsNotNone(entry)
        self.assertEqual(entry.source_text, "Hello")
        self.assertEqual(entry.target_text, "Hola")
        self.assertEqual(entry.confidence, 0.95)
    
    def test_batch_operations(self):
        """Test batch translation operations"""
        # Store multiple translations
        from src.agents.smart_translator_agent.models.translation_memory import TranslationEntry
        from datetime import datetime
        
        entries = [
            TranslationEntry(
                source_text="Hello",
                target_text="Hola",
                source_language="en",
                target_language="es",
                provider="azure",
                confidence=0.95,
                created_at=datetime.now(),
                last_used=datetime.now()
            ),
            TranslationEntry(
                source_text="World",
                target_text="Mundo",
                source_language="en",
                target_language="es",
                provider="azure",
                confidence=0.90,
                created_at=datetime.now(),
                last_used=datetime.now()
            )
        ]
        
        self.memory.store_batch_translations(entries)
        
        # Retrieve batch
        requests = [("Hello", "en", "es"), ("World", "en", "es")]
        results = self.memory.get_batch_translations(requests)
        
        self.assertEqual(len(results), 2)


if __name__ == '__main__':
    unittest.main()
