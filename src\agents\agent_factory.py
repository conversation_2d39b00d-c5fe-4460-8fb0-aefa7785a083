from enum import Enum
from typing import List, Optional

from src.agents.text_to_sql_agent.text_to_sql import Text2SQL
from src.agents.rag_agent.rag import RAG
from src.agents.rag_agent.rag_document import RAGDocument
from src.agents.eproexcella_agent.eproexcella import EPROExcelLa
from src.agents.smart_translator_agent.smart_translator import SmartTranslatorAgent






class AgentFactory(Enum):
    TEXT_TO_SQL = 'text_to_sql'
    RAG = 'rag'
    RAG_DOCUMENT = 'rag_document'
    EPROEXCELLA = "eproexcella"
    SMART_TRANSLATOR = "smart_translator"

    def create_rag_agent(agent_name, bot_name, **kwargs):
        return RAG(bot_name, agent_name, **kwargs)

    def create_rag_document_agent(agent_name, bot_name, **kwargs):
        return RAGDocument(bot_name, agent_name, **kwargs)

    def create_text_to_sql_agent(agent_name, bot_name, **kwargs):
        return Text2SQL(bot_name, agent_name, **kwargs )

    def create_eproexcella( agent_name, bot_name, **kwargs):
        return EPROExcelLa(bot_name, agent_name, **kwargs)

    def create_smart_translator(agent_name, bot_name, **kwargs):
        return SmartTranslatorAgent(bot_name, agent_name, **kwargs)
    
    def create(self,agent_name,  bot_name, **kwargs):

        if agent_name == AgentFactory.TEXT_TO_SQL.name:
            return self.create_text_to_sql_agent( agent_name, bot_name, **kwargs)
        elif agent_name == AgentFactory.RAG.name:
            return self.create_rag_agent( agent_name, bot_name, **kwargs)
        elif agent_name == AgentFactory.RAG_DOCUMENT.name:
            return self.create_rag_document_agent(agent_name, bot_name, **kwargs)
        elif agent_name == AgentFactory.EPROEXCELLA.name:
            return self.create_eproexcella( agent_name, bot_name, **kwargs)
        elif agent_name == AgentFactory.SMART_TRANSLATOR.name:
            return self.create_smart_translator(agent_name, bot_name, **kwargs)
    
    def reverse_agent_factory_mapping(agent_value):
        for agent in AgentFactory:
            if agent_value == agent.value:
                return agent.name
