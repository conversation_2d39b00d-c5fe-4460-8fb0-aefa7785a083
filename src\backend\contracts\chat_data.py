from dataclasses import dataclass, field
from enum import Enum
from typing import Iterator, List, Optional, Union

from utils.core import get_relative_path


class ResponseStatus(str, Enum):
    OK = "ok"
    ERROR = "error"


class UserRole(Enum):
    ADMIN = 1
    USER = 2


class ApproachType(Enum):
    structured = "1"
    unstructured = "2"
    chit_chat = "3"
    continuation = "4"
    inappropriate = "5"


class AnswerStatus(str, Enum):
    SUCCESS = "SUCCESS"
    ERROR = "ERROR"
    STOPPED = "STOPPED"


class AnswerFeedback(str, Enum):
    GOOD = "GOOD"  # 👍🏼
    NEUTRAL = "NEUTRAL"  # 😐
    BAD = "BAD"  # 👎🏼

class SQLAnswerType(Enum):
    MARKDOWN = "markdown"
    JSON = "json"
    HTML = "html"

class RAGAnswerType(Enum):
    MARKDOWN = "markdown"
    HTML = "html"

@dataclass
class BotTypeMixin:
    display_name: str
    tables: Iterator = field(default=iter([]))
    db_schema_path: str = field(default=None)


class BotType(BotTypeMixin, Enum):
    DUMMY = "Dummy"
    COMPLI_BOT = (
        "Complibot",
        iter(
            [
                "AGREEMENT_VIEW",
                "BOM",
                "COMMERCIAL_PRODUCTS",
                "ITEM_COMPLIANCES",
                "ITEM_MASTER",
                "SUPPLIER",
            ]
        ),
        get_relative_path(
            __file__, f"../../agents/text_to_sql_agent/models/COMPLI_BOT_schema.json"
        ),
    )
    CALL_CENTER_BOT = (
        "Call Center Bot",
        iter(
            [
                "BOM",
                "COMMERCIAL_PRODUCTS",
                "ITEM_MASTER",
                "SUPPLIER",
                "INSTALLED_BASE",
                "SERIAL_NUMBERS",
                "SALES_ORDER_VIEW",
                "RELATED_SPARE_PARTS",
                "COMMERCIAL_PRODUCTS_HIERARCHY",
                "COMMERCIAL_PRODUCTS_DOCUMENTS",
                "BOM_PRMS",
                "get_hierarchy_data",
                "SERVICE_JOBS_COMPLETE_LIST",
                "PRIDE_PDB_MAINDATA_IMPROVED",
                "PRIDE_PDB_LOGISTIC_IMPROVED"
            ]
        ),
        get_relative_path(
            __file__,
            f"../../agents/text_to_sql_agent/models/CALL_CENTER_BOT_schema.json",
        ),
    )
    APPLICATION_HOWTO_BOT = "Application How-To"
    EPROEXCELLA_BOT = "EPROExcelLa"
    SMART_TRANSLATOR_BOT = "Smart Translator"
    JDAILO = "JD-AI-lo"
    SEO_BOT = "SEO Bot"

    def __repr__(self):
        return self.value.display_name


class AgentName(str, Enum):
    TEXT_TO_SQL = "text_to_sql"
    RAG = "rag"
    RAG_DOCUMENT = "rag_document"
    EPROEXCELLA = "eproexcella"
    SMART_TRANSLATOR = "smart_translator"

class SelectionObj:
    def __init__(
        self,
        is_multiple: bool,
        choices: List[str]
    ):
        self.is_multiple = is_multiple
        self.choices = choices

    def to_json(self):
        return self.__dict__    
    
class Answer:
    def __init__(
        self,
        formatted_answer: Union[str, dict] = "",
        query_generation_prompt: Optional[str] = None,
        query: Optional[str] = None,
        query_result: Optional[str] = None,
        explanation: Optional[str] = None,
        source_file: Optional[str] = None,
        agent: str = "",
        answer_type: str = "html"
    ):
        self.formatted_answer = formatted_answer
        self.query_generation_prompt = query_generation_prompt
        self.query = query
        self.query_result = query_result
        self.explanation = explanation
        self.source_file = source_file
        self.agent = agent
        self.answer_type = answer_type

    def to_json(self):
        return {
            "formatted_answer": self.formatted_answer,
            "query_generation_prompt": self.query_generation_prompt,
            "query": self.query,
            "query_result": self.query_result,
            "explanation": self.explanation,
            "source_file": self.source_file,
            "agent": self.agent,
            "answer_type": self.answer_type
        }


class File:
    def __init__(
        self,
        name: str = "",
        path: str = "",
    ):
        self.name = name
        self.path = path

    def __eq__(self, other):
        if isinstance(other, File):
            return self.name == other.name and self.path == other.path
        return False

    def __hash__(self):
        return hash((self.name, self.path))

    def to_json(self):
        return {"name": self.name, "path": self.path}



class ChatResponse:
    def __init__(
        self,
        answer: Optional[Answer] = None,
        appendix: Optional[str] = None,
        question: Optional[str]= None,
        classification: Optional[ApproachType] = None,
        data_points: List[str] = [],
        error: Optional[str] = None,
        suggested_classification: Optional[ApproachType] = None,
        show_retry: bool = False,
        files: List[File] = [],
        dialog_id: str = "",
        images: List[str] = [],
        selection: Optional[SelectionObj] = None,
        source_file_details: Optional[dict] = {}
    ):
        self.answer = answer
        self.appendix = appendix
        self.question = question
        self.classification = classification
        self.data_points = data_points
        self.error = error
        self.suggested_classification = suggested_classification
        self.show_retry = show_retry
        self.files = files
        self.dialog_id = dialog_id
        self.images = images
        self.selection = selection
        self.source_file_details = source_file_details

    def to_json(self):
        return {
            "classification": self.classification,
            "appendix": self.appendix,
            "question": self.question,
            "answer": self.answer.to_json(),
            "data_points": [str(data_point) for data_point in self.data_points],
            "error": self.error,
            "suggested_classification": self.suggested_classification,
            "show_retry": self.show_retry,
            "files": [file.to_json() for file in self.files],
            "dialog_id": self.dialog_id,
            "images": self.images,
            "selection": self.selection.to_json() if self.selection else None,
            "source_file_details": self.source_file_details
        }

class ChatExtraDetails:
    def __init__(
        self,
        bot_type: BotType,
        feedback: Optional[AnswerFeedback] = None,
        status: AnswerStatus = AnswerStatus.SUCCESS,
        duration: float = None,
    ):
        self.bot_type = bot_type
        self.feedback = feedback
        self.status = status
        self.duration = duration

    def to_json(self):
        return self.__dict__


class SampleQuestionDetails:
    def __init__(
        self,
        bot_type: BotType,
    ):
        self.bot_type = bot_type

    def to_json(self):
        return self.__dict__


class SampleQuestionDetails:
    def __init__(
        self,
        bot_type: BotType,
    ):
        self.bot_type = bot_type

    def to_json(self):
        return self.__dict__


class SampleQuestionDetails:
    def __init__(
        self,
        bot_type: BotType,
    ):
        self.bot_type = bot_type

    def to_json(self):
        return self.__dict__


class SampleQuestionDetails:
    def __init__(
        self,
        bot_type: BotType,
    ):
        self.bot_type = bot_type

    def to_json(self):
        return self.__dict__


class ChatQuestion:
    def __init__(
        self,
        question: str,
        question_status: str = None
    ):
        self.question = question
        self.question_status = question_status

    def to_json(self):
        return self.__dict__


class BotTypeResponse:
    def __init__(
        self,
        bot_type: List[BotType],
    ):
        self.bot_type = bot_type

    def to_json(self):
        return {i.name: i.display_name for i in self.bot_type}


class GenericResponse:
    def __init__(
        self,
        status: ResponseStatus,
        code: Optional[str] = None,
        message: Optional[str] = None,
    ):
        self.status = status
        self.code = code
        self.message = message

    def to_json(self):
        return self.__dict__
