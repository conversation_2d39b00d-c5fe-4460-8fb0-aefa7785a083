"""
Document Processor for Smart Translator Agent

Handles different document formats and extracts translatable content.
"""

import os
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
import json
from pathlib import Path

from utils.core import get_logger

logger = get_logger(__file__)


class DocumentType(Enum):
    EXCEL = "xlsx"
    WORD = "docx"
    POWERPOINT = "pptx"
    PDF = "pdf"
    TEXT = "txt"
    CSV = "csv"


@dataclass
class TranslatableContent:
    """Represents content that can be translated"""
    content: str
    location: str  # Where this content is located (e.g., "Sheet1:A1", "Slide1:Title")
    content_type: str  # Type of content (text, table_cell, title, etc.)
    metadata: Dict[str, Any] = None


@dataclass
class DocumentStructure:
    """Represents the structure of a document"""
    document_type: DocumentType
    translatable_content: List[TranslatableContent]
    metadata: Dict[str, Any]
    original_structure: Any  # Original document structure for reconstruction


class DocumentProcessor(ABC):
    """Abstract base class for document processors"""
    
    @abstractmethod
    def can_process(self, file_path: str) -> bool:
        """Check if this processor can handle the given file"""
        pass
    
    @abstractmethod
    def extract_content(self, file_path: str) -> DocumentStructure:
        """Extract translatable content from the document"""
        pass
    
    @abstractmethod
    def reconstruct_document(self, structure: DocumentStructure, translations: Dict[str, str]) -> Any:
        """Reconstruct the document with translations"""
        pass


class ExcelProcessor(DocumentProcessor):
    """Processor for Excel files"""
    
    def can_process(self, file_path: str) -> bool:
        return file_path.lower().endswith('.xlsx')
    
    def extract_content(self, file_path: str) -> DocumentStructure:
        """Extract translatable content from Excel file"""
        try:
            # Read all sheets
            excel_file = pd.ExcelFile(file_path)
            translatable_content = []
            sheets_data = {}
            
            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                sheets_data[sheet_name] = df
                
                # Extract text content from each cell
                for row_idx, row in df.iterrows():
                    for col_idx, cell_value in enumerate(row):
                        if pd.notna(cell_value) and isinstance(cell_value, str) and cell_value.strip():
                            location = f"{sheet_name}:{df.columns[col_idx]}:{row_idx}"
                            content = TranslatableContent(
                                content=str(cell_value).strip(),
                                location=location,
                                content_type="table_cell",
                                metadata={
                                    "sheet": sheet_name,
                                    "column": df.columns[col_idx],
                                    "row": row_idx
                                }
                            )
                            translatable_content.append(content)
            
            return DocumentStructure(
                document_type=DocumentType.EXCEL,
                translatable_content=translatable_content,
                metadata={"sheets": list(excel_file.sheet_names)},
                original_structure=sheets_data
            )
            
        except Exception as e:
            logger.error(f"Error processing Excel file {file_path}: {e}")
            raise
    
    def reconstruct_document(self, structure: DocumentStructure, translations: Dict[str, str]) -> Dict[str, pd.DataFrame]:
        """Reconstruct Excel file with translations"""
        try:
            sheets_data = structure.original_structure.copy()
            
            # Apply translations
            for content in structure.translatable_content:
                if content.location in translations:
                    sheet_name = content.metadata["sheet"]
                    column = content.metadata["column"]
                    row = content.metadata["row"]
                    
                    sheets_data[sheet_name].at[row, column] = translations[content.location]
            
            return sheets_data
            
        except Exception as e:
            logger.error(f"Error reconstructing Excel document: {e}")
            raise


class TextProcessor(DocumentProcessor):
    """Processor for plain text files"""
    
    def can_process(self, file_path: str) -> bool:
        return file_path.lower().endswith(('.txt', '.csv'))
    
    def extract_content(self, file_path: str) -> DocumentStructure:
        """Extract content from text file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # For now, treat the entire file as one translatable unit
            # Could be enhanced to split by paragraphs, sentences, etc.
            translatable_content = [
                TranslatableContent(
                    content=content,
                    location="full_text",
                    content_type="text",
                    metadata={"file_type": "text"}
                )
            ]
            
            return DocumentStructure(
                document_type=DocumentType.TEXT,
                translatable_content=translatable_content,
                metadata={"encoding": "utf-8"},
                original_structure=content
            )
            
        except Exception as e:
            logger.error(f"Error processing text file {file_path}: {e}")
            raise
    
    def reconstruct_document(self, structure: DocumentStructure, translations: Dict[str, str]) -> str:
        """Reconstruct text file with translations"""
        try:
            if "full_text" in translations:
                return translations["full_text"]
            return structure.original_structure
            
        except Exception as e:
            logger.error(f"Error reconstructing text document: {e}")
            raise


class DocumentProcessorFactory:
    """Factory for creating document processors"""
    
    _processors = [
        ExcelProcessor(),
        TextProcessor(),
        # Add more processors as needed
    ]
    
    @classmethod
    def get_processor(cls, file_path: str) -> Optional[DocumentProcessor]:
        """Get appropriate processor for the file"""
        for processor in cls._processors:
            if processor.can_process(file_path):
                return processor
        return None
    
    @classmethod
    def get_supported_formats(cls) -> List[str]:
        """Get list of supported file formats"""
        formats = []
        for processor in cls._processors:
            if hasattr(processor, 'supported_formats'):
                formats.extend(processor.supported_formats)
        return ['.xlsx', '.txt', '.csv']  # Hardcoded for now, could be dynamic
