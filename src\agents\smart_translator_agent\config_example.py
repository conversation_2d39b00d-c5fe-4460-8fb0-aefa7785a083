"""
Configuration Example for Smart Translator Agent

This file shows how to configure the Smart Translator Agent with different settings.
Copy this file to config.py and modify as needed.
"""

from src.agents.smart_translator_agent.models.translation_service import TranslationProvider

# Basic Configuration
SMART_TRANSLATOR_CONFIG = {
    # Translation provider settings
    'translation_provider': TranslationProvider.AZURE_TRANSLATOR,
    
    # Translation memory settings
    'use_translation_memory': True,
    'translation_memory_max_entries': 10000,
    'translation_memory_max_age_days': 30,
    
    # Batch processing settings
    'batch_size': 100,
    'max_concurrent_requests': 10,
    'max_concurrent_jobs': 3,
    'max_concurrent_files': 5,
    
    # Progress tracking settings
    'enable_progress_tracking': True,
    'progress_cleanup_hours': 24,
    
    # Language detection settings
    'auto_detect_language': True,
    'confidence_threshold': 0.5,
    
    # File handling settings
    'upload_directory': 'upload',
    'output_directory': 'output',
    'supported_formats': ['.xlsx', '.docx', '.pptx', '.txt', '.csv'],
    'max_file_size_mb': 50,
    
    # History settings
    'history_max_age_days': 90,
    'history_cleanup_enabled': True,
}

# Azure Translator Specific Configuration
AZURE_TRANSLATOR_CONFIG = {
    'endpoint': 'https://api.cognitive.microsofttranslator.com',
    'key': 'your_azure_translator_key_here',
    'region': 'global',  # or your specific region
    'api_version': '3.0',
    'timeout_seconds': 30,
    'retry_attempts': 3,
    'retry_delay_seconds': 1,
}

# Database Configuration
DATABASE_CONFIG = {
    # Translation memory database
    'translation_memory_db': 'cache/translation_memory.db',
    
    # Translation history database
    'translation_history_db': 'cache/translation_history.db',
    
    # Database connection settings
    'db_timeout_seconds': 30,
    'db_max_connections': 10,
    'db_cleanup_interval_hours': 6,
}

# Logging Configuration
LOGGING_CONFIG = {
    'level': 'INFO',  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'logs/smart_translator.log',
    'max_file_size_mb': 10,
    'backup_count': 5,
    'console_output': True,
}

# Performance Configuration
PERFORMANCE_CONFIG = {
    # Memory management
    'max_memory_usage_mb': 1024,
    'memory_cleanup_threshold': 0.8,
    
    # Caching
    'enable_result_caching': True,
    'cache_max_size': 1000,
    'cache_ttl_seconds': 3600,
    
    # Threading
    'thread_pool_size': 10,
    'async_timeout_seconds': 300,
    
    # Rate limiting
    'rate_limit_requests_per_minute': 100,
    'rate_limit_enabled': True,
}

# Security Configuration
SECURITY_CONFIG = {
    # File upload security
    'allowed_file_extensions': ['.xlsx', '.docx', '.pptx', '.txt', '.csv'],
    'scan_uploaded_files': True,
    'quarantine_suspicious_files': True,
    
    # API security
    'require_authentication': True,
    'api_key_required': False,
    'rate_limiting_enabled': True,
    
    # Data protection
    'encrypt_stored_translations': False,
    'anonymize_user_data': True,
    'data_retention_days': 90,
}

# Development Configuration
DEVELOPMENT_CONFIG = {
    'debug_mode': False,
    'mock_translation_service': False,
    'test_data_enabled': False,
    'profiling_enabled': False,
    'verbose_logging': False,
}

# Production Configuration
PRODUCTION_CONFIG = {
    'debug_mode': False,
    'error_reporting_enabled': True,
    'monitoring_enabled': True,
    'health_check_enabled': True,
    'metrics_collection_enabled': True,
}

# Feature Flags
FEATURE_FLAGS = {
    'batch_processing_enabled': True,
    'translation_memory_enabled': True,
    'progress_tracking_enabled': True,
    'history_tracking_enabled': True,
    'advanced_analytics_enabled': True,
    'real_time_updates_enabled': True,
    'multi_language_ui_enabled': False,
    'api_versioning_enabled': False,
}

# UI Configuration
UI_CONFIG = {
    # Color scheme (Corporate/Golden/Warm Grey/Deep Teal/Sustainability)
    'primary_color': '#14133B',      # Corporate primary
    'secondary_color': '#433D6B',    # Corporate secondary
    'accent_color': '#cea941',       # Golden primary
    'success_color': '#00C89A',      # Sustainability primary
    'warning_color': '#BFAF8F',      # Golden secondary
    'error_color': '#FF6B6B',        # Custom error color
    
    # Layout settings
    'max_upload_files': 10,
    'preview_rows_limit': 10,
    'progress_update_interval_ms': 1000,
    'auto_refresh_enabled': True,
    
    # User experience
    'show_advanced_options': True,
    'enable_drag_drop': True,
    'show_progress_details': True,
    'enable_keyboard_shortcuts': True,
}

# Notification Configuration
NOTIFICATION_CONFIG = {
    'email_notifications_enabled': False,
    'email_smtp_server': 'smtp.gmail.com',
    'email_smtp_port': 587,
    'email_username': '<EMAIL>',
    'email_password': 'your_app_password',
    
    'webhook_notifications_enabled': False,
    'webhook_url': 'https://your-webhook-url.com/notifications',
    
    'in_app_notifications_enabled': True,
    'notification_retention_days': 7,
}

# Monitoring Configuration
MONITORING_CONFIG = {
    'metrics_enabled': True,
    'metrics_endpoint': '/metrics',
    'health_check_endpoint': '/health',
    
    'track_translation_metrics': True,
    'track_performance_metrics': True,
    'track_error_metrics': True,
    'track_user_metrics': True,
    
    'alert_on_high_error_rate': True,
    'alert_on_slow_performance': True,
    'alert_on_service_unavailable': True,
}

# Integration Configuration
INTEGRATION_CONFIG = {
    # External services
    'google_translate_enabled': False,
    'google_translate_api_key': 'your_google_api_key',
    
    'openai_translation_enabled': False,
    'openai_api_key': 'your_openai_api_key',
    
    # Webhooks
    'translation_complete_webhook': None,
    'batch_complete_webhook': None,
    'error_webhook': None,
    
    # File storage
    'cloud_storage_enabled': False,
    'aws_s3_bucket': 'your-s3-bucket',
    'azure_blob_container': 'your-blob-container',
}

# Complete configuration combining all sections
COMPLETE_CONFIG = {
    **SMART_TRANSLATOR_CONFIG,
    'azure_translator': AZURE_TRANSLATOR_CONFIG,
    'database': DATABASE_CONFIG,
    'logging': LOGGING_CONFIG,
    'performance': PERFORMANCE_CONFIG,
    'security': SECURITY_CONFIG,
    'development': DEVELOPMENT_CONFIG,
    'production': PRODUCTION_CONFIG,
    'features': FEATURE_FLAGS,
    'ui': UI_CONFIG,
    'notifications': NOTIFICATION_CONFIG,
    'monitoring': MONITORING_CONFIG,
    'integrations': INTEGRATION_CONFIG,
}

# Environment-specific configurations
ENVIRONMENTS = {
    'development': {
        **COMPLETE_CONFIG,
        **DEVELOPMENT_CONFIG,
        'logging': {**LOGGING_CONFIG, 'level': 'DEBUG'},
    },
    
    'testing': {
        **COMPLETE_CONFIG,
        'use_translation_memory': False,
        'enable_progress_tracking': False,
        'mock_translation_service': True,
    },
    
    'staging': {
        **COMPLETE_CONFIG,
        **PRODUCTION_CONFIG,
        'logging': {**LOGGING_CONFIG, 'level': 'INFO'},
    },
    
    'production': {
        **COMPLETE_CONFIG,
        **PRODUCTION_CONFIG,
        'logging': {**LOGGING_CONFIG, 'level': 'WARNING'},
    }
}

def get_config(environment='development'):
    """Get configuration for specific environment"""
    return ENVIRONMENTS.get(environment, ENVIRONMENTS['development'])

def validate_config(config):
    """Validate configuration settings"""
    required_keys = [
        'translation_provider',
        'use_translation_memory',
        'batch_size',
        'enable_progress_tracking'
    ]
    
    for key in required_keys:
        if key not in config:
            raise ValueError(f"Missing required configuration key: {key}")
    
    # Validate Azure Translator config if using Azure
    if config['translation_provider'] == TranslationProvider.AZURE_TRANSLATOR:
        azure_config = config.get('azure_translator', {})
        if not azure_config.get('key'):
            raise ValueError("Azure Translator API key is required")
        if not azure_config.get('endpoint'):
            raise ValueError("Azure Translator endpoint is required")
    
    return True

# Example usage:
if __name__ == '__main__':
    # Get development configuration
    dev_config = get_config('development')
    
    # Validate configuration
    try:
        validate_config(dev_config)
        print("Configuration is valid!")
    except ValueError as e:
        print(f"Configuration error: {e}")
    
    # Print some configuration values
    print(f"Translation provider: {dev_config['translation_provider']}")
    print(f"Batch size: {dev_config['batch_size']}")
    print(f"Progress tracking: {dev_config['enable_progress_tracking']}")
