"""
Translation Service for Smart Translator Agent

Manages different translation providers and handles translation requests.
"""

import requests
import uuid
from typing import List, Dict, Optional, Tuple
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor
import time

from config.config import EproExcelLaConfig
from utils.core import get_logger, Singleton

logger = get_logger(__file__)


class TranslationProvider(Enum):
    AZURE_TRANSLATOR = "azure"
    GOOGLE_TRANSLATE = "google"
    OPENAI_TRANSLATE = "openai"


@dataclass
class TranslationRequest:
    """Represents a translation request"""
    text: str
    source_language: str
    target_language: str
    location: str  # Location identifier for tracking
    metadata: Dict = None


@dataclass
class TranslationResult:
    """Represents a translation result"""
    original_text: str
    translated_text: str
    source_language: str
    target_language: str
    location: str
    confidence: float = 0.0
    provider: str = ""
    metadata: Dict = None


class TranslationService(ABC):
    """Abstract base class for translation services"""
    
    @abstractmethod
    async def translate_batch(self, requests: List[TranslationRequest]) -> List[TranslationResult]:
        """Translate a batch of requests"""
        pass
    
    @abstractmethod
    def translate_single(self, request: TranslationRequest) -> TranslationResult:
        """Translate a single request"""
        pass
    
    @abstractmethod
    def get_supported_languages(self) -> List[str]:
        """Get list of supported language codes"""
        pass


class AzureTranslationService(TranslationService):
    """Azure Translator service implementation"""
    
    def __init__(self):
        self.config = EproExcelLaConfig()
        self.endpoint = self.config.translator_azure_ai_api_translator_endpoint
        self.key = self.config.translator_azure_ai_api_translator_key
        self.region = getattr(self.config, 'translator_azure_region', 'global')
        
        # Common headers for Azure Translator
        self.headers = {
            'Ocp-Apim-Subscription-Key': self.key,
            'Ocp-Apim-Subscription-Region': self.region,
            'Content-type': 'application/json',
            'X-ClientTraceId': str(uuid.uuid4())
        }
    
    def translate_single(self, request: TranslationRequest) -> TranslationResult:
        """Translate a single text"""
        try:
            # Construct the request
            path = '/translate'
            constructed_url = self.endpoint + path
            
            params = {
                'api-version': '3.0',
                'from': request.source_language if request.source_language != 'auto' else None,
                'to': request.target_language
            }
            
            body = [{'text': request.text}]
            
            response = requests.post(
                constructed_url, 
                params=params, 
                headers=self.headers, 
                json=body
            )
            response.raise_for_status()
            
            result = response.json()
            
            if result and len(result) > 0:
                translation = result[0]['translations'][0]
                detected_language = result[0].get('detectedLanguage', {}).get('language', request.source_language)
                
                return TranslationResult(
                    original_text=request.text,
                    translated_text=translation['text'],
                    source_language=detected_language,
                    target_language=request.target_language,
                    location=request.location,
                    confidence=result[0].get('detectedLanguage', {}).get('score', 0.0),
                    provider="azure",
                    metadata=request.metadata
                )
            else:
                raise Exception("Empty response from Azure Translator")
                
        except Exception as e:
            logger.error(f"Azure translation error for location {request.location}: {e}")
            # Return original text as fallback
            return TranslationResult(
                original_text=request.text,
                translated_text=request.text,
                source_language=request.source_language,
                target_language=request.target_language,
                location=request.location,
                confidence=0.0,
                provider="azure",
                metadata={"error": str(e)}
            )
    
    async def translate_batch(self, requests: List[TranslationRequest]) -> List[TranslationResult]:
        """Translate a batch of requests asynchronously"""
        try:
            # Group requests by language pair for efficiency
            grouped_requests = {}
            for req in requests:
                key = f"{req.source_language}->{req.target_language}"
                if key not in grouped_requests:
                    grouped_requests[key] = []
                grouped_requests[key].append(req)
            
            results = []
            
            # Process each language pair group
            for lang_pair, group_requests in grouped_requests.items():
                # Split into smaller batches (Azure has limits)
                batch_size = 100
                for i in range(0, len(group_requests), batch_size):
                    batch = group_requests[i:i + batch_size]
                    batch_results = await self._translate_batch_azure(batch)
                    results.extend(batch_results)
            
            return results
            
        except Exception as e:
            logger.error(f"Batch translation error: {e}")
            # Return original texts as fallback
            return [
                TranslationResult(
                    original_text=req.text,
                    translated_text=req.text,
                    source_language=req.source_language,
                    target_language=req.target_language,
                    location=req.location,
                    confidence=0.0,
                    provider="azure",
                    metadata={"error": str(e)}
                )
                for req in requests
            ]
    
    async def _translate_batch_azure(self, requests: List[TranslationRequest]) -> List[TranslationResult]:
        """Internal method to translate a batch using Azure API"""
        if not requests:
            return []
        
        try:
            # Use the first request's language settings for the batch
            first_request = requests[0]
            
            path = '/translate'
            constructed_url = self.endpoint + path
            
            params = {
                'api-version': '3.0',
                'from': first_request.source_language if first_request.source_language != 'auto' else None,
                'to': first_request.target_language
            }
            
            body = [{'text': req.text} for req in requests]
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    constructed_url,
                    params=params,
                    headers=self.headers,
                    json=body
                ) as response:
                    response.raise_for_status()
                    result = await response.json()
            
            # Process results
            results = []
            for i, (req, translation_result) in enumerate(zip(requests, result)):
                if translation_result and 'translations' in translation_result:
                    translation = translation_result['translations'][0]
                    detected_language = translation_result.get('detectedLanguage', {}).get('language', req.source_language)
                    
                    results.append(TranslationResult(
                        original_text=req.text,
                        translated_text=translation['text'],
                        source_language=detected_language,
                        target_language=req.target_language,
                        location=req.location,
                        confidence=translation_result.get('detectedLanguage', {}).get('score', 0.0),
                        provider="azure",
                        metadata=req.metadata
                    ))
                else:
                    # Fallback for failed translation
                    results.append(TranslationResult(
                        original_text=req.text,
                        translated_text=req.text,
                        source_language=req.source_language,
                        target_language=req.target_language,
                        location=req.location,
                        confidence=0.0,
                        provider="azure",
                        metadata={"error": "Translation failed"}
                    ))
            
            return results
            
        except Exception as e:
            logger.error(f"Azure batch translation error: {e}")
            # Return original texts as fallback
            return [
                TranslationResult(
                    original_text=req.text,
                    translated_text=req.text,
                    source_language=req.source_language,
                    target_language=req.target_language,
                    location=req.location,
                    confidence=0.0,
                    provider="azure",
                    metadata={"error": str(e)}
                )
                for req in requests
            ]
    
    def get_supported_languages(self) -> List[str]:
        """Get supported languages from Azure Translator"""
        try:
            path = '/languages'
            constructed_url = self.endpoint + path
            params = {'api-version': '3.0'}
            
            response = requests.get(constructed_url, params=params)
            response.raise_for_status()
            
            result = response.json()
            return list(result.get('translation', {}).keys())
            
        except Exception as e:
            logger.error(f"Error getting supported languages: {e}")
            # Return common languages as fallback
            return ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh']


class TranslationServiceFactory:
    """Factory for creating translation services"""
    
    @staticmethod
    def create_service(provider: TranslationProvider = TranslationProvider.AZURE_TRANSLATOR) -> TranslationService:
        """Create a translation service instance"""
        if provider == TranslationProvider.AZURE_TRANSLATOR:
            return AzureTranslationService()
        else:
            raise ValueError(f"Unsupported translation provider: {provider}")
    
    @staticmethod
    def get_available_providers() -> List[TranslationProvider]:
        """Get list of available translation providers"""
        return [TranslationProvider.AZURE_TRANSLATOR]
