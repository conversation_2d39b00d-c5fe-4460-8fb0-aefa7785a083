<!-- templates/translator_bot/smart_translation_tool.html -->

{% extends "base.html" %}

{% block head_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
    :root {
        /* Corporate/Golden/Warm Grey/Deep Teal/Sustainability color palette */
        --corporate-primary: #14133B;
        --corporate-secondary: #433D6B;
        --golden-primary: #cea941;
        --golden-secondary: #BFAF8F;
        --warm-grey-primary: #8d8060;
        --warm-grey-light: #bfbab0;
        --warm-grey-lighter: #d9d5d2;
        --deep-teal-primary: #24639F;
        --deep-teal-light: #526D70;
        --sustainability-primary: #00C89A;
        --sustainability-light: #4DDBC4;
    }

    .smart-translation-container {
        background: linear-gradient(135deg, rgba(36, 99, 159, 0.05) 0%, rgba(82, 109, 112, 0.05) 100%);
        min-height: calc(100vh - 120px);
        padding: 2rem 0;
    }

    .smart-translation-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(20, 19, 59, 0.1);
        border: 1px solid var(--warm-grey-lighter);
        overflow: hidden;
    }

    .card-header-smart {
        background: linear-gradient(135deg, var(--corporate-primary) 0%, var(--corporate-secondary) 100%);
        color: white;
        padding: 2rem;
        text-align: center;
        border: none;
    }

    .upload-area {
        border: 3px dashed var(--deep-teal-light);
        border-radius: 12px;
        padding: 3rem 2rem;
        text-align: center;
        background: linear-gradient(135deg, rgba(36, 99, 159, 0.02) 0%, rgba(0, 200, 154, 0.02) 100%);
        transition: all 0.3s ease;
        cursor: pointer;
        margin: 2rem 0;
    }

    .upload-area:hover {
        border-color: var(--sustainability-primary);
        background: linear-gradient(135deg, rgba(36, 99, 159, 0.05) 0%, rgba(0, 200, 154, 0.05) 100%);
        transform: translateY(-2px);
    }

    .upload-area.dragover {
        border-color: var(--golden-primary);
        background: linear-gradient(135deg, rgba(206, 169, 65, 0.1) 0%, rgba(191, 175, 143, 0.1) 100%);
    }

    .upload-icon {
        font-size: 3rem;
        color: var(--deep-teal-primary);
        margin-bottom: 1rem;
    }

    .file-info {
        background: linear-gradient(135deg, rgba(0, 200, 154, 0.05) 0%, rgba(77, 219, 196, 0.05) 100%);
        border: 1px solid var(--sustainability-light);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1rem 0;
    }

    .language-selection {
        background: var(--warm-grey-lighter);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1.5rem 0;
    }

    .btn-smart-primary {
        background: linear-gradient(135deg, var(--corporate-primary) 0%, var(--corporate-secondary) 100%);
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-smart-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(20, 19, 59, 0.3);
        color: white;
    }

    .btn-smart-secondary {
        background: linear-gradient(135deg, var(--golden-primary) 0%, var(--golden-secondary) 100%);
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-smart-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(206, 169, 65, 0.3);
        color: white;
    }

    .progress-container {
        margin-top: 2rem;
        padding: 1.5rem;
        background: linear-gradient(135deg, rgba(36, 99, 159, 0.05) 0%, rgba(82, 109, 112, 0.05) 100%);
        border-radius: 12px;
        border: 1px solid var(--deep-teal-light);
    }

    .progress-smart {
        height: 8px;
        border-radius: 4px;
        background-color: var(--warm-grey-light);
    }

    .progress-bar-smart {
        background: linear-gradient(90deg, var(--deep-teal-primary) 0%, var(--sustainability-primary) 100%);
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .status-text {
        color: var(--corporate-primary);
        font-weight: 500;
        margin-top: 0.5rem;
    }

    .column-selector {
        background: white;
        border: 1px solid var(--warm-grey-light);
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
        max-height: 200px;
        overflow-y: auto;
    }

    .form-check-input:checked {
        background-color: var(--sustainability-primary);
        border-color: var(--sustainability-primary);
    }

    .alert-smart-info {
        background: linear-gradient(135deg, rgba(36, 99, 159, 0.1) 0%, rgba(0, 200, 154, 0.1) 100%);
        border: 1px solid var(--deep-teal-light);
        color: var(--corporate-primary);
        border-radius: 8px;
    }

    .alert-smart-success {
        background: linear-gradient(135deg, rgba(0, 200, 154, 0.1) 0%, rgba(77, 219, 196, 0.1) 100%);
        border: 1px solid var(--sustainability-light);
        color: var(--corporate-primary);
        border-radius: 8px;
    }

    .alert-smart-warning {
        background: linear-gradient(135deg, rgba(206, 169, 65, 0.1) 0%, rgba(191, 175, 143, 0.1) 100%);
        border: 1px solid var(--golden-secondary);
        color: var(--corporate-primary);
        border-radius: 8px;
    }

    .form-control:focus {
        border-color: var(--sustainability-primary);
        box-shadow: 0 0 0 0.2rem rgba(0, 200, 154, 0.25);
    }

    .form-select:focus {
        border-color: var(--sustainability-primary);
        box-shadow: 0 0 0 0.2rem rgba(0, 200, 154, 0.25);
    }

    .results-container {
        margin-top: 2rem;
        padding: 1.5rem;
        background: white;
        border-radius: 12px;
        border: 1px solid var(--warm-grey-light);
    }

    .table-smart {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(20, 19, 59, 0.1);
    }

    .table-smart thead th {
        background: linear-gradient(135deg, var(--corporate-primary) 0%, var(--corporate-secondary) 100%);
        color: white;
        border: none;
        font-weight: 500;
    }

    .table-smart tbody tr:hover {
        background-color: rgba(0, 200, 154, 0.05);
    }

    .metadata-info {
        background: var(--warm-grey-lighter);
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .feature-highlight {
        background: linear-gradient(135deg, rgba(206, 169, 65, 0.05) 0%, rgba(191, 175, 143, 0.05) 100%);
        border-left: 4px solid var(--golden-primary);
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 0 8px 8px 0;
    }

    .spinner-smart {
        color: var(--sustainability-primary);
    }

    #toggleAllColumns {
        background: none;
        border: none;
        color: var(--deep-teal-primary);
        font-size: 0.9rem;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        transition: all 0.2s ease;
    }

    #toggleAllColumns:hover {
        background-color: rgba(36, 99, 159, 0.1);
        color: var(--corporate-primary);
    }

    #toggleAllColumns:hover i {
        transform: scale(1.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="smart-translation-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-8">
                <div class="smart-translation-card">
                    <div class="card-header-smart">
                        <h1 class="mb-0">
                            <i class="fas fa-language me-3"></i>
                            Smart Document Translation Tool
                        </h1>
                        <p class="mb-0 mt-2 opacity-75">
                            Enhanced AI-powered translation with progress tracking and multi-format support
                        </p>
                    </div>

                    <div class="card-body p-4">
                        <!-- Feature Highlights -->
                        <div class="feature-highlight">
                            <h6 class="mb-2"><i class="fas fa-star text-warning me-2"></i>Enhanced Features</h6>
                            <ul class="mb-0 small">
                                <li>Real-time progress tracking</li>
                                <li>Translation memory for consistency</li>
                                <li>Support for Excel, Word, PowerPoint, and text files</li>
                                <li>Batch processing capabilities</li>
                            </ul>
                        </div>

                        <!-- File Upload Section -->
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <h5 class="mb-3">Drop your file here or click to browse</h5>
                            <p class="text-muted mb-3">
                                Supports: Excel (.xlsx), Word (.docx), PowerPoint (.pptx), Text (.txt), CSV (.csv)
                            </p>
                            <input type="file" id="fileInput" class="d-none" accept=".xlsx,.docx,.pptx,.txt,.csv">
                            <button type="button" class="btn btn-smart-secondary" onclick="document.getElementById('fileInput').click()">
                                <i class="fas fa-folder-open me-2"></i>Choose File
                            </button>
                        </div>

                        <!-- File Information -->
                        <div id="fileInfo" class="file-info" style="display: none;">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-2">
                                        <i class="fas fa-file me-2"></i>
                                        <span id="fileName"></span>
                                    </h6>
                                    <div id="fileDetails" class="small text-muted"></div>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-danger" id="removeFile">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Language Selection -->
                        <div class="language-selection">
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="sourceLanguage" class="form-label">
                                        <i class="fas fa-globe me-2"></i>Source Language
                                    </label>
                                    <select class="form-select" id="sourceLanguage">
                                        <option value="auto">Auto-detect</option>
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                        <option value="zh">Chinese</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="targetLanguage" class="form-label">
                                        <i class="fas fa-language me-2"></i>Target Language *
                                    </label>
                                    <select class="form-select" id="targetLanguage" required>
                                        <option value="">Select target language</option>
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                        <option value="zh">Chinese</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Excel Column Selection (shown only for Excel files) -->
                        <div id="excelOptions" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label mb-0">
                                    <i class="fas fa-columns me-2"></i>Select Columns to Translate
                                </label>
                                <button type="button" id="toggleAllColumns" title="Toggle all columns">
                                    <i class="fas fa-check-double"></i> Toggle All
                                </button>
                            </div>
                            <div id="columnCheckboxes" class="column-selector">
                                <!-- Column checkboxes will be populated here -->
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-3 justify-content-center mt-4">
                            <button type="button" class="btn btn-smart-secondary" id="previewBtn">
                                <i class="fas fa-eye me-2"></i>Preview Translation
                            </button>
                            <button type="button" class="btn btn-smart-primary" id="translateBtn">
                                <i class="fas fa-language me-2"></i>Start Translation
                            </button>
                        </div>

                        <!-- Progress Section -->
                        <div id="progressSection" class="progress-container" style="display: none;">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">
                                    <i class="fas fa-tasks me-2"></i>Translation Progress
                                </h6>
                                <button type="button" class="btn btn-sm btn-outline-danger" id="cancelBtn">
                                    <i class="fas fa-stop me-1"></i>Cancel
                                </button>
                            </div>
                            <div class="progress progress-smart mb-2">
                                <div class="progress-bar progress-bar-smart" id="progressBar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="status-text" id="statusText">Initializing...</div>
                            <div class="small text-muted mt-1" id="progressDetails"></div>
                        </div>

                        <!-- Results Section -->
                        <div id="resultsSection" class="results-container" style="display: none;">
                            <h6 class="mb-3">
                                <i class="fas fa-check-circle text-success me-2"></i>Translation Results
                            </h6>
                            <div id="resultsContent"></div>
                            <div class="mt-3 text-center">
                                <button type="button" class="btn btn-smart-primary" id="downloadBtn">
                                    <i class="fas fa-download me-2"></i>Download Translated File
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block tail_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileDetails = document.getElementById('fileDetails');
    const removeFile = document.getElementById('removeFile');
    const excelOptions = document.getElementById('excelOptions');
    const columnCheckboxes = document.getElementById('columnCheckboxes');
    const toggleAllColumns = document.getElementById('toggleAllColumns');
    const previewBtn = document.getElementById('previewBtn');
    const translateBtn = document.getElementById('translateBtn');
    const targetLanguage = document.getElementById('targetLanguage');
    const sourceLanguage = document.getElementById('sourceLanguage');
    const progressSection = document.getElementById('progressSection');
    const progressBar = document.getElementById('progressBar');
    const statusText = document.getElementById('statusText');
    const progressDetails = document.getElementById('progressDetails');
    const cancelBtn = document.getElementById('cancelBtn');
    const resultsSection = document.getElementById('resultsSection');
    const resultsContent = document.getElementById('resultsContent');
    const downloadBtn = document.getElementById('downloadBtn');

    let selectedFile = null;
    let currentTaskId = null;
    let progressInterval = null;

    // Load supported languages on page load
    loadSupportedLanguages();

    // File upload handling
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    fileInput.addEventListener('change', handleFileSelect);
    removeFile.addEventListener('click', clearFile);

    // Column selection
    toggleAllColumns.addEventListener('click', toggleAllColumnSelection);

    // Action buttons
    previewBtn.addEventListener('click', previewTranslation);
    translateBtn.addEventListener('click', startTranslation);
    cancelBtn.addEventListener('click', cancelTranslation);
    downloadBtn.addEventListener('click', downloadTranslatedFile);

    function loadSupportedLanguages() {
        fetch('/translator/smart/api/languages')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    populateLanguageSelects(data.supported_languages);
                }
            })
            .catch(error => {
                console.error('Error loading languages:', error);
            });
    }

    function populateLanguageSelects(languages) {
        const sourceSelect = document.getElementById('sourceLanguage');
        const targetSelect = document.getElementById('targetLanguage');

        // Clear existing options (except first ones)
        while (sourceSelect.children.length > 1) {
            sourceSelect.removeChild(sourceSelect.lastChild);
        }
        while (targetSelect.children.length > 1) {
            targetSelect.removeChild(targetSelect.lastChild);
        }

        // Add language options
        languages.forEach(lang => {
            const languageNames = {
                'en': 'English',
                'es': 'Spanish',
                'fr': 'French',
                'de': 'German',
                'it': 'Italian',
                'pt': 'Portuguese',
                'ru': 'Russian',
                'ja': 'Japanese',
                'ko': 'Korean',
                'zh': 'Chinese'
            };

            const name = languageNames[lang] || lang.toUpperCase();

            const sourceOption = new Option(name, lang);
            const targetOption = new Option(name, lang);

            sourceSelect.add(sourceOption);
            targetSelect.add(targetOption);
        });
    }

    function handleDragOver(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    }

    function handleDragLeave(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    }

    function handleDrop(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileUpload(files[0]);
        }
    }

    function handleFileSelect(e) {
        const files = e.target.files;
        if (files.length > 0) {
            handleFileUpload(files[0]);
        }
    }

    function handleFileUpload(file) {
        selectedFile = file;

        const formData = new FormData();
        formData.append('file', file);

        // Show loading state
        uploadArea.innerHTML = \`
            <div class="upload-icon">
                <i class="fas fa-spinner fa-spin"></i>
            </div>
            <h5 class="mb-3">Uploading file...</h5>
        \`;

        fetch('/translator/smart/api/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayFileInfo(data);
                resetUploadArea();
            } else {
                showError('Upload failed: ' + data.error);
                resetUploadArea();
            }
        })
        .catch(error => {
            showError('Upload error: ' + error.message);
            resetUploadArea();
        });
    }

    function resetUploadArea() {
        uploadArea.innerHTML = \`
            <div class="upload-icon">
                <i class="fas fa-cloud-upload-alt"></i>
            </div>
            <h5 class="mb-3">Drop your file here or click to browse</h5>
            <p class="text-muted mb-3">
                Supports: Excel (.xlsx), Word (.docx), PowerPoint (.pptx), Text (.txt), CSV (.csv)
            </p>
            <button type="button" class="btn btn-smart-secondary" onclick="document.getElementById('fileInput').click()">
                <i class="fas fa-folder-open me-2"></i>Choose File
            </button>
        \`;
    }

    // Additional functions will be added in the next part...

    function showError(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-smart-warning alert-dismissible fade show mt-3';
        alertDiv.innerHTML = \`
            <i class="fas fa-exclamation-triangle me-2"></i>
            \${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        \`;

        document.querySelector('.card-body').insertBefore(alertDiv, document.querySelector('.upload-area'));

        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
});
</script>
{% endblock %}
