"""
Smart Translator Bot

Enhanced bot that works with the Smart Translator Agent, providing better orchestration
and management capabilities for translation tasks.
"""

import pandas as pd
from typing import Dict, Any, Optional
from flask import current_app

from src.bots.bot import Bo<PERSON>
from src.agents.agent_factory import AgentFactory
from src.agents.answer import Answer
from src.backend.contracts.chat_data import BotType
from src.common_tools.history.history import ConversationHistory
from src.bots.conversation_session import ConversationSession
from utils.core import get_logger, Singleton

logger = get_logger(__file__)


class SmartTranslatorBot(Bot, metaclass=Singleton):
    """Enhanced translator bot with improved capabilities"""
    
    def __init__(self, agents, bot_name: str, **kwargs):
        """Initialize the Smart Translator Bot"""
        super().__init__(agents, bot_name, **kwargs)
        self.user_id = kwargs.get('user_id', '')
        logger.info(f"Smart Translator Bot initialized for user {self.user_id}")
    
    def handle(self, question: str, web_response, history: ConversationHistory, 
               conversation_session: ConversationSession, bot_config_handler: dict):
        """
        Handle translation requests with enhanced capabilities
        
        Args:
            question: Translation request in natural language
            web_response: Web response object to populate
            history: Conversation history
            conversation_session: Current conversation session
            bot_config_handler: Bot configuration settings
        
        Returns:
            Tuple of (web_response, stream_answer, extra_details, reply)
        """
        try:
            # Extract configuration
            preview = bot_config_handler.get("preview", False)
            user_id = bot_config_handler.get("user_id", self.user_id)
            file_path = bot_config_handler.get("file_path")
            target_language = bot_config_handler.get("target_language", "en")
            source_language = bot_config_handler.get("source_language", "auto")
            selected_columns = bot_config_handler.get("selected_columns", [])
            
            logger.info(f"Processing translation request: {question}")
            logger.info(f"Config: preview={preview}, target_lang={target_language}, user_id={user_id}")
            
            # Call the Smart Translator Agent
            reply = self.call_agent(
                AgentFactory.SMART_TRANSLATOR.name,
                inquiry=question,
                history=history,
                preview=preview,
                user_id=user_id,
                file_path=file_path,
                target_language=target_language,
                source_language=source_language,
                selected_columns=selected_columns
            )
            
            # Process the response based on the result
            if reply.status == "completed":
                # Format the response for display
                formatted_answer = self._format_translation_response(reply, preview)
                
                web_response.answer = Answer(
                    formatted_answer=formatted_answer,
                    agent=reply.agent_name
                )
                
                # Add task information if available
                if reply.task_id:
                    web_response.task_id = reply.task_id
                    web_response.translation_status = "completed"
                
                # Add metadata
                if hasattr(web_response, 'metadata'):
                    web_response.metadata = reply.metadata
                
                web_response.suggested_classification = "Translation completed"
                
            else:
                # Handle error case
                error_message = reply.info or "Translation failed"
                web_response.answer = Answer(
                    formatted_answer=f"<div class='alert alert-danger'>{error_message}</div>",
                    agent=reply.agent_name
                )
                web_response.suggested_classification = "Translation failed"
            
            logger.info(f"Translation request processed. Status: {reply.status}")
            return web_response, False, None, reply
            
        except Exception as e:
            logger.error(f"Error in Smart Translator Bot: {e}")
            
            # Create error response
            web_response.answer = Answer(
                formatted_answer=f"<div class='alert alert-danger'>Translation error: {str(e)}</div>",
                agent="smart_translator"
            )
            web_response.suggested_classification = "Error"
            
            return web_response, False, None, None
    
    def _format_translation_response(self, reply, preview: bool = False) -> str:
        """Format the translation response for display"""
        try:
            if reply.data:
                # Try to parse as JSON (for Excel files)
                try:
                    data_df = pd.read_json(reply.data)
                    
                    # Limit rows for preview
                    if preview:
                        data_df = data_df.head(10)
                        preview_note = f"<div class='alert alert-info mb-3'><i class='fas fa-info-circle'></i> Showing preview of first 10 rows. Total items translated: {reply.metadata.get('total_items', 'unknown')}</div>"
                    else:
                        preview_note = f"<div class='alert alert-success mb-3'><i class='fas fa-check-circle'></i> Translation completed. Total items translated: {reply.metadata.get('total_items', 'unknown')}</div>"
                    
                    # Create HTML table with Bootstrap styling
                    table_html = data_df.to_html(
                        index=False,
                        classes='table table-striped table-hover table-sm',
                        table_id='translationResults'
                    )
                    
                    # Add some styling and metadata
                    metadata_info = ""
                    if reply.metadata:
                        metadata_info = f"""
                        <div class='row mb-3'>
                            <div class='col-md-6'>
                                <small class='text-muted'>
                                    <strong>Source Language:</strong> {reply.metadata.get('source_language', 'auto')}<br>
                                    <strong>Target Language:</strong> {reply.metadata.get('target_language', 'unknown')}<br>
                                    <strong>Document Type:</strong> {reply.metadata.get('document_type', 'unknown')}
                                </small>
                            </div>
                            <div class='col-md-6'>
                                <small class='text-muted'>
                                    <strong>Total Items:</strong> {reply.metadata.get('total_items', 'unknown')}<br>
                                    <strong>Preview Mode:</strong> {'Yes' if reply.metadata.get('preview') else 'No'}
                                </small>
                            </div>
                        </div>
                        """
                    
                    return f"""
                    <div class='translation-results'>
                        {preview_note}
                        {metadata_info}
                        <div class='table-responsive'>
                            {table_html}
                        </div>
                    </div>
                    """
                    
                except (ValueError, TypeError):
                    # If not JSON, treat as plain text
                    return f"""
                    <div class='translation-results'>
                        <div class='alert alert-success mb-3'>
                            <i class='fas fa-check-circle'></i> Translation completed
                        </div>
                        <div class='card'>
                            <div class='card-body'>
                                <pre class='mb-0'>{reply.data}</pre>
                            </div>
                        </div>
                    </div>
                    """
            else:
                return f"""
                <div class='alert alert-warning'>
                    <i class='fas fa-exclamation-triangle'></i> 
                    Translation completed but no data returned. {reply.info}
                </div>
                """
                
        except Exception as e:
            logger.error(f"Error formatting translation response: {e}")
            return f"""
            <div class='alert alert-danger'>
                <i class='fas fa-exclamation-circle'></i> 
                Error formatting translation results: {str(e)}
            </div>
            """
    
    def get_task_progress(self, task_id: str) -> Optional[Dict]:
        """Get progress for a specific translation task"""
        try:
            agent = self.get_agent(AgentFactory.SMART_TRANSLATOR.name)
            return agent.get_task_progress(task_id)
        except Exception as e:
            logger.error(f"Error getting task progress: {e}")
            return None
    
    def cancel_task(self, task_id: str):
        """Cancel a running translation task"""
        try:
            agent = self.get_agent(AgentFactory.SMART_TRANSLATOR.name)
            agent.cancel_task(task_id)
            logger.info(f"Cancelled translation task {task_id}")
        except Exception as e:
            logger.error(f"Error cancelling task: {e}")
    
    def get_supported_formats(self) -> list:
        """Get list of supported file formats"""
        try:
            agent = self.get_agent(AgentFactory.SMART_TRANSLATOR.name)
            return agent.get_supported_formats()
        except Exception as e:
            logger.error(f"Error getting supported formats: {e}")
            return ['.xlsx', '.docx', '.pptx', '.txt', '.csv']
    
    def get_supported_languages(self) -> list:
        """Get list of supported languages"""
        try:
            agent = self.get_agent(AgentFactory.SMART_TRANSLATOR.name)
            return agent.get_supported_languages()
        except Exception as e:
            logger.error(f"Error getting supported languages: {e}")
            return ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh']
    
    def validate_translation_request(self, request_data: Dict) -> Dict[str, Any]:
        """Validate a translation request and return validation results"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Check required fields
        if not request_data.get('target_language'):
            validation_result["valid"] = False
            validation_result["errors"].append("Target language is required")
        
        # Check file format support
        file_type = request_data.get('file_type', '')
        supported_formats = self.get_supported_formats()
        if file_type and file_type not in supported_formats:
            validation_result["valid"] = False
            validation_result["errors"].append(f"Unsupported file format: {file_type}")
        
        # Check language support
        target_lang = request_data.get('target_language')
        supported_languages = self.get_supported_languages()
        if target_lang and target_lang not in supported_languages:
            validation_result["warnings"].append(f"Target language '{target_lang}' may not be fully supported")
        
        source_lang = request_data.get('source_language')
        if source_lang and source_lang != 'auto' and source_lang not in supported_languages:
            validation_result["warnings"].append(f"Source language '{source_lang}' may not be fully supported")
        
        return validation_result
