#!/usr/bin/env python3
"""
Test Runner for Smart Translator Agent

Simple script to run tests for the Smart Translator Agent system.
"""

import sys
import os
import unittest
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

def run_tests():
    """Run all tests for the Smart Translator Agent"""
    
    print("=" * 60)
    print("Smart Translator Agent Test Suite")
    print("=" * 60)
    
    # Discover and run tests
    test_dir = Path(__file__).parent / "tests"
    
    if not test_dir.exists():
        print(f"Test directory not found: {test_dir}")
        return False
    
    # Create test loader
    loader = unittest.TestLoader()
    
    # Discover tests
    suite = loader.discover(str(test_dir), pattern='test_*.py')
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("\n✅ All tests passed!")
    else:
        print("\n❌ Some tests failed!")
    
    return success

def run_specific_test(test_name):
    """Run a specific test"""
    
    print(f"Running specific test: {test_name}")
    print("=" * 60)
    
    # Import the test module
    try:
        from tests.test_smart_translator import *
        
        # Create test suite with specific test
        suite = unittest.TestSuite()
        
        # Add specific test if provided
        if '::' in test_name:
            class_name, method_name = test_name.split('::')
            test_class = globals().get(class_name)
            if test_class:
                suite.addTest(test_class(method_name))
            else:
                print(f"Test class not found: {class_name}")
                return False
        else:
            # Run all tests in a class
            test_class = globals().get(test_name)
            if test_class:
                suite.addTest(unittest.makeSuite(test_class))
            else:
                print(f"Test class not found: {test_name}")
                return False
        
        # Run the test
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return len(result.failures) == 0 and len(result.errors) == 0
        
    except ImportError as e:
        print(f"Error importing test module: {e}")
        return False

def main():
    """Main function"""
    
    if len(sys.argv) > 1:
        # Run specific test
        test_name = sys.argv[1]
        success = run_specific_test(test_name)
    else:
        # Run all tests
        success = run_tests()
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
