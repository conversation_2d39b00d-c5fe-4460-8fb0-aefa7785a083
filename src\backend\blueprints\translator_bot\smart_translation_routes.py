"""
Enhanced Translation Routes for Smart Translator Bo<PERSON>

Provides improved API endpoints with better error handling, progress tracking,
and support for multiple document formats.
"""

from flask import Blueprint, render_template, request, jsonify, current_app, session
from werkzeug.utils import secure_filename
import os
import pandas as pd
from pathlib import Path
import uuid
from typing import Dict, Any

from src.backend.blueprints.auth.decorators import login_epr
from src.agents.agent_factory import AgentFactory
from src.bots.smart_translator_bot import SmartTranslatorBot
from src.backend.contracts.chat_data import BotType
from src.backend.models import User
from src import db
from utils.core import get_logger

logger = get_logger(__file__)

smart_translation_routes = Blueprint('smart_translation_routes', __name__, template_folder='templates')


@smart_translation_routes.route('/')
@login_epr
def index():
    """Enhanced translation tool page"""
    current_app.logger.info("Smart Translation tool accessed")
    return render_template('translator_bot/smart_translation_tool.html')


@smart_translation_routes.route('/api/upload', methods=['POST'])
@login_epr
def upload_file():
    """Enhanced file upload with better validation and support for multiple formats"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Get user ID from session
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Create Smart Translator Bot to get supported formats
        smart_translator_bot = SmartTranslatorBot(
            agents=[AgentFactory.SMART_TRANSLATOR.name],
            bot_name=BotType.SMART_TRANSLATOR_BOT.name,
            user_id=user_id
        )
        
        supported_formats = smart_translator_bot.get_supported_formats()
        
        # Validate file type
        file_extension = '.' + file.filename.rsplit('.', 1)[1].lower()
        if file_extension not in supported_formats:
            return jsonify({
                'error': f'Unsupported file type: {file_extension}',
                'supported_formats': supported_formats
            }), 400

        # Create upload directory if it doesn't exist
        upload_dir = Path(__file__).parent.parent.parent.parent / 'agents' / 'smart_translator_agent' / 'upload'
        upload_dir.mkdir(parents=True, exist_ok=True)

        # Generate secure filename
        filename = secure_filename(file.filename)
        file_path = upload_dir / f"{user_id}_{filename}"
        
        # Save file
        file.save(str(file_path))
        
        # Analyze file and extract metadata
        file_info = _analyze_uploaded_file(str(file_path), file_extension)
        
        current_app.logger.info(f"File uploaded successfully: {filename} for user {user_id}")
        
        return jsonify({
            'success': True,
            'message': 'File uploaded successfully',
            'filename': filename,
            'file_type': file_extension,
            'file_info': file_info,
            'supported_languages': smart_translator_bot.get_supported_languages()
        })

    except Exception as e:
        current_app.logger.error(f"File upload error: {e}")
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500


def _analyze_uploaded_file(file_path: str, file_extension: str) -> Dict[str, Any]:
    """Analyze uploaded file and return metadata"""
    try:
        file_info = {
            'size': os.path.getsize(file_path),
            'type': file_extension,
            'columns': [],
            'sheets': [],
            'estimated_items': 0
        }
        
        if file_extension == '.xlsx':
            # Analyze Excel file
            excel_file = pd.ExcelFile(file_path)
            file_info['sheets'] = excel_file.sheet_names
            
            # Get columns from first sheet
            if excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=excel_file.sheet_names[0], nrows=5)
                file_info['columns'] = df.columns.tolist()
                
                # Estimate total translatable items
                for sheet_name in excel_file.sheet_names:
                    sheet_df = pd.read_excel(file_path, sheet_name=sheet_name)
                    # Count non-empty string cells
                    for col in sheet_df.columns:
                        if sheet_df[col].dtype == 'object':
                            file_info['estimated_items'] += sheet_df[col].notna().sum()
        
        elif file_extension == '.csv':
            # Analyze CSV file
            df = pd.read_csv(file_path, nrows=5)
            file_info['columns'] = df.columns.tolist()
            
            # Estimate items
            full_df = pd.read_csv(file_path)
            for col in full_df.columns:
                if full_df[col].dtype == 'object':
                    file_info['estimated_items'] += full_df[col].notna().sum()
        
        elif file_extension == '.txt':
            # Analyze text file
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                file_info['estimated_items'] = 1  # Treat as single item
                file_info['preview'] = content[:500] + '...' if len(content) > 500 else content
        
        return file_info
        
    except Exception as e:
        logger.error(f"Error analyzing file: {e}")
        return {'error': str(e)}


@smart_translation_routes.route('/api/translate', methods=['POST'])
@login_epr
def translate_document():
    """Enhanced document translation with progress tracking"""
    try:
        data = request.get_json()
        
        # Validate request
        validation_result = _validate_translation_request(data)
        if not validation_result['valid']:
            return jsonify({
                'error': 'Invalid request',
                'details': validation_result['errors']
            }), 400

        # Get user ID from session
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']
        
        # Extract parameters
        target_language = data['target_language']
        source_language = data.get('source_language', 'auto')
        selected_columns = data.get('selected_columns', [])
        file_type = data.get('file_type', '')
        filename = data.get('filename', '')
        
        # Build file path
        upload_dir = Path(__file__).parent.parent.parent.parent / 'agents' / 'smart_translator_agent' / 'upload'
        file_path = upload_dir / f"{user_id}_{filename}" if filename else upload_dir / f"data_{user_id}.xlsx"
        
        if not file_path.exists():
            return jsonify({'error': 'File not found. Please upload a file first.'}), 404
        
        # Create Smart Translator Bot
        smart_translator_bot = SmartTranslatorBot(
            agents=[AgentFactory.SMART_TRANSLATOR.name],
            bot_name=BotType.SMART_TRANSLATOR_BOT.name,
            user_id=user_id
        )
        
        # Build natural language request
        if selected_columns:
            column_names = ', '.join(selected_columns)
            if len(selected_columns) == 1:
                translation_request = f"Translate the column {column_names} from {source_language} to {target_language}"
            else:
                translation_request = f"Translate the columns {column_names} from {source_language} to {target_language}"
        else:
            translation_request = f"Translate all text content from {source_language} to {target_language}"
        
        # Process translation
        reply = smart_translator_bot.call_agent(
            AgentFactory.SMART_TRANSLATOR.name,
            inquiry=translation_request,
            history=None,
            preview=False,
            user_id=user_id,
            file_path=str(file_path),
            target_language=target_language,
            source_language=source_language,
            selected_columns=selected_columns
        )
        
        if reply.status == "completed":
            current_app.logger.info(f"Translation completed for user {user_id}")
            
            return jsonify({
                'success': True,
                'message': 'Translation completed successfully',
                'task_id': reply.task_id,
                'status': reply.status,
                'data': reply.data,
                'metadata': reply.metadata,
                'download_url': f'/translator/smart/api/download/{reply.task_id}' if reply.task_id else None
            })
        else:
            return jsonify({
                'success': False,
                'message': reply.info or 'Translation failed',
                'task_id': reply.task_id,
                'status': reply.status,
                'error': reply.info
            }), 500
        
    except Exception as e:
        current_app.logger.error(f"Translation error: {e}")
        return jsonify({'error': f'Translation failed: {str(e)}'}), 500


def _validate_translation_request(data: Dict) -> Dict[str, Any]:
    """Validate translation request data"""
    validation_result = {
        'valid': True,
        'errors': [],
        'warnings': []
    }
    
    # Check required fields
    if not data or 'target_language' not in data:
        validation_result['valid'] = False
        validation_result['errors'].append('Target language is required')
    
    # Additional validation can be added here
    
    return validation_result


@smart_translation_routes.route('/api/progress/<task_id>')
@login_epr
def get_translation_progress(task_id):
    """Get real-time translation progress"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']
        
        # Create Smart Translator Bot
        smart_translator_bot = SmartTranslatorBot(
            agents=[AgentFactory.SMART_TRANSLATOR.name],
            bot_name=BotType.SMART_TRANSLATOR_BOT.name,
            user_id=user_id
        )
        
        # Get progress
        progress = smart_translator_bot.get_task_progress(task_id)
        
        if progress:
            return jsonify({
                'success': True,
                'progress': progress
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Task not found'
            }), 404
        
    except Exception as e:
        current_app.logger.error(f"Progress check error: {e}")
        return jsonify({'error': 'Progress check failed'}), 500


@smart_translation_routes.route('/api/cancel/<task_id>', methods=['POST'])
@login_epr
def cancel_translation(task_id):
    """Cancel a running translation task"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']
        
        # Create Smart Translator Bot
        smart_translator_bot = SmartTranslatorBot(
            agents=[AgentFactory.SMART_TRANSLATOR.name],
            bot_name=BotType.SMART_TRANSLATOR_BOT.name,
            user_id=user_id
        )
        
        # Cancel task
        smart_translator_bot.cancel_task(task_id)
        
        return jsonify({
            'success': True,
            'message': 'Translation cancelled successfully'
        })
        
    except Exception as e:
        current_app.logger.error(f"Cancel error: {e}")
        return jsonify({'error': 'Cancel failed'}), 500


@smart_translation_routes.route('/api/download/<task_id>')
@login_epr
def download_translated_file(task_id):
    """Download translated file"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # For now, use the same download logic as the original translator
        # This could be enhanced to support different file formats
        upload_dir = Path(__file__).parent.parent.parent.parent / 'agents' / 'smart_translator_agent' / 'upload'
        file_path = upload_dir / f"data_{user_id}.xlsx"

        if file_path.exists():
            from flask import send_file
            return send_file(
                str(file_path),
                as_attachment=True,
                download_name=f"translated_file_{user_id}.xlsx",
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
        else:
            return jsonify({'error': 'Translated file not found'}), 404

    except Exception as e:
        current_app.logger.error(f"Download error: {e}")
        return jsonify({'error': 'Download failed'}), 500


@smart_translation_routes.route('/api/preview', methods=['POST'])
@login_epr
def preview_translation():
    """Preview translation with enhanced capabilities"""
    try:
        data = request.get_json()
        current_app.logger.info(f"Preview request data: {data}")

        if not data or 'translation_request' not in data:
            current_app.logger.error(f"Invalid preview request data: {data}")
            return jsonify({'error': 'Translation request is required'}), 400

        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        translation_request = data['translation_request']
        target_language = data.get('target_language', 'en')
        source_language = data.get('source_language', 'auto')
        selected_columns = data.get('selected_columns', [])
        filename = data.get('filename', '')

        # Build file path
        upload_dir = Path(__file__).parent.parent.parent.parent / 'agents' / 'smart_translator_agent' / 'upload'
        file_path = upload_dir / f"{user_id}_{filename}" if filename else upload_dir / f"data_{user_id}.xlsx"

        # Create Smart Translator Bot for preview
        smart_translator_bot = SmartTranslatorBot(
            agents=[AgentFactory.SMART_TRANSLATOR.name],
            bot_name=BotType.SMART_TRANSLATOR_BOT.name,
            user_id=user_id
        )

        # Process with preview=True for limited results
        reply = smart_translator_bot.call_agent(
            AgentFactory.SMART_TRANSLATOR.name,
            inquiry=translation_request,
            history=None,
            preview=True,
            user_id=user_id,
            file_path=str(file_path),
            target_language=target_language,
            source_language=source_language,
            selected_columns=selected_columns
        )

        if reply.status == "completed":
            # Convert JSON data to DataFrame for preview
            try:
                data_json = pd.read_json(reply.data)
                preview_html = data_json.head(10).to_html(
                    index=False,
                    classes='table table-striped table-hover table-sm'
                )

                return jsonify({
                    'success': True,
                    'preview_html': preview_html,
                    'total_rows': len(data_json),
                    'columns': data_json.columns.tolist(),
                    'metadata': reply.metadata,
                    'task_id': reply.task_id
                })
            except Exception as e:
                # If not JSON data, return as text
                return jsonify({
                    'success': True,
                    'preview_html': f'<pre>{reply.data}</pre>',
                    'metadata': reply.metadata,
                    'task_id': reply.task_id
                })
        else:
            return jsonify({
                'success': False,
                'error': reply.info or 'Preview failed'
            }), 500

    except Exception as e:
        current_app.logger.error(f"Preview error: {e}")
        return jsonify({'error': 'Preview failed'}), 500


@smart_translation_routes.route('/api/languages')
@login_epr
def get_supported_languages():
    """Get list of supported languages"""
    try:
        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Create Smart Translator Bot
        smart_translator_bot = SmartTranslatorBot(
            agents=[AgentFactory.SMART_TRANSLATOR.name],
            bot_name=BotType.SMART_TRANSLATOR_BOT.name,
            user_id=user_id
        )

        languages = smart_translator_bot.get_supported_languages()
        formats = smart_translator_bot.get_supported_formats()

        return jsonify({
            'success': True,
            'supported_languages': languages,
            'supported_formats': formats
        })

    except Exception as e:
        current_app.logger.error(f"Languages error: {e}")
        return jsonify({'error': 'Failed to get supported languages'}), 500


@smart_translation_routes.route('/api/validate', methods=['POST'])
@login_epr
def validate_translation_request():
    """Validate translation request before processing"""
    try:
        data = request.get_json()

        user = session.get('user')
        if not user:
            return jsonify({'error': 'User not authenticated'}), 401
        user_id = user['id']

        # Create Smart Translator Bot
        smart_translator_bot = SmartTranslatorBot(
            agents=[AgentFactory.SMART_TRANSLATOR.name],
            bot_name=BotType.SMART_TRANSLATOR_BOT.name,
            user_id=user_id
        )

        # Validate request
        validation_result = smart_translator_bot.validate_translation_request(data)

        return jsonify({
            'success': True,
            'validation': validation_result
        })

    except Exception as e:
        current_app.logger.error(f"Validation error: {e}")
        return jsonify({'error': 'Validation failed'}), 500
