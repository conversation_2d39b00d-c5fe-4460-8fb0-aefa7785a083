"""
Batch Processor for Smart Translator Agent

Handles batch processing of multiple files for translation.
"""

import os
import asyncio
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import uuid
from concurrent.futures import ThreadPoolExecutor
import threading

from .document_processor import DocumentProcessorFactory, DocumentStructure
from .translation_service import TranslationServiceFactory, TranslationProvider
from .progress_tracker import progress_tracker, TaskStatus
from .translation_history import translation_history, TranslationHistoryEntry
from utils.core import get_logger

logger = get_logger(__file__)


class BatchStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class BatchFileItem:
    """Represents a single file in a batch"""
    file_path: str
    filename: str
    file_type: str
    status: BatchStatus = BatchStatus.PENDING
    task_id: Optional[str] = None
    error_message: Optional[str] = None
    result_data: Optional[Any] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class BatchTranslationJob:
    """Represents a batch translation job"""
    batch_id: str
    user_id: str
    source_language: str
    target_language: str
    files: List[BatchFileItem]
    status: BatchStatus = BatchStatus.PENDING
    created_at: Optional[str] = None
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    total_files: int = 0
    processed_files: int = 0
    failed_files: int = 0
    progress: float = 0.0
    error_message: Optional[str] = None
    
    def __post_init__(self):
        self.total_files = len(self.files)


class BatchProcessor:
    """Handles batch processing of translation jobs"""
    
    def __init__(self, max_concurrent_jobs: int = 3, max_concurrent_files: int = 5):
        self.max_concurrent_jobs = max_concurrent_jobs
        self.max_concurrent_files = max_concurrent_files
        self.active_jobs: Dict[str, BatchTranslationJob] = {}
        self.job_lock = threading.Lock()
        self.executor = ThreadPoolExecutor(max_workers=max_concurrent_jobs)
        
        # Initialize services
        self.document_processor_factory = DocumentProcessorFactory()
        self.translation_service = TranslationServiceFactory.create_service()
        
        logger.info(f"Batch processor initialized with {max_concurrent_jobs} concurrent jobs")
    
    def create_batch_job(self, user_id: str, file_paths: List[str], 
                        source_language: str, target_language: str,
                        **kwargs) -> str:
        """Create a new batch translation job"""
        batch_id = str(uuid.uuid4())
        
        # Create file items
        files = []
        for file_path in file_paths:
            filename = os.path.basename(file_path)
            file_extension = '.' + filename.rsplit('.', 1)[1].lower()
            
            files.append(BatchFileItem(
                file_path=file_path,
                filename=filename,
                file_type=file_extension,
                metadata=kwargs.get('file_metadata', {})
            ))
        
        # Create batch job
        job = BatchTranslationJob(
            batch_id=batch_id,
            user_id=user_id,
            source_language=source_language,
            target_language=target_language,
            files=files,
            created_at=str(asyncio.get_event_loop().time())
        )
        
        with self.job_lock:
            self.active_jobs[batch_id] = job
        
        logger.info(f"Created batch job {batch_id} with {len(files)} files for user {user_id}")
        return batch_id
    
    def start_batch_job(self, batch_id: str, progress_callback: Optional[Callable] = None) -> bool:
        """Start processing a batch job"""
        with self.job_lock:
            if batch_id not in self.active_jobs:
                logger.error(f"Batch job {batch_id} not found")
                return False
            
            job = self.active_jobs[batch_id]
            if job.status != BatchStatus.PENDING:
                logger.error(f"Batch job {batch_id} is not in pending status")
                return False
            
            job.status = BatchStatus.PROCESSING
            job.started_at = str(asyncio.get_event_loop().time())
        
        # Submit job to executor
        future = self.executor.submit(self._process_batch_job, batch_id, progress_callback)
        
        logger.info(f"Started batch job {batch_id}")
        return True
    
    def _process_batch_job(self, batch_id: str, progress_callback: Optional[Callable] = None):
        """Process a batch job (runs in thread pool)"""
        try:
            with self.job_lock:
                job = self.active_jobs[batch_id]
            
            logger.info(f"Processing batch job {batch_id} with {job.total_files} files")
            
            # Process files concurrently
            asyncio.run(self._process_files_async(job, progress_callback))
            
            # Update final status
            with self.job_lock:
                if job.failed_files == 0:
                    job.status = BatchStatus.COMPLETED
                elif job.processed_files > 0:
                    job.status = BatchStatus.COMPLETED  # Partial success
                else:
                    job.status = BatchStatus.FAILED
                
                job.completed_at = str(asyncio.get_event_loop().time())
                job.progress = 1.0
            
            logger.info(f"Completed batch job {batch_id}: {job.processed_files}/{job.total_files} files processed")
            
        except Exception as e:
            logger.error(f"Error processing batch job {batch_id}: {e}")
            
            with self.job_lock:
                job = self.active_jobs[batch_id]
                job.status = BatchStatus.FAILED
                job.error_message = str(e)
                job.completed_at = str(asyncio.get_event_loop().time())
    
    async def _process_files_async(self, job: BatchTranslationJob, progress_callback: Optional[Callable] = None):
        """Process files asynchronously with concurrency control"""
        semaphore = asyncio.Semaphore(self.max_concurrent_files)
        
        async def process_single_file(file_item: BatchFileItem):
            async with semaphore:
                await self._process_single_file(job, file_item, progress_callback)
        
        # Create tasks for all files
        tasks = [process_single_file(file_item) for file_item in job.files]
        
        # Wait for all tasks to complete
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _process_single_file(self, job: BatchTranslationJob, file_item: BatchFileItem, 
                                 progress_callback: Optional[Callable] = None):
        """Process a single file in the batch"""
        try:
            logger.info(f"Processing file {file_item.filename} in batch {job.batch_id}")
            
            # Update file status
            file_item.status = BatchStatus.PROCESSING
            file_item.task_id = str(uuid.uuid4())
            
            # Create progress tracking task
            task_id = progress_tracker.create_task(job.user_id, file_item.filename, 0)
            
            # Add to translation history
            history_entry = TranslationHistoryEntry(
                user_id=job.user_id,
                task_id=task_id,
                filename=file_item.filename,
                file_type=file_item.file_type,
                source_language=job.source_language,
                target_language=job.target_language,
                status="processing"
            )
            translation_history.add_entry(history_entry)
            
            try:
                # Get document processor
                processor = self.document_processor_factory.get_processor(file_item.file_path)
                if not processor:
                    raise ValueError(f"Unsupported file format: {file_item.file_type}")
                
                # Extract content
                extract_step = progress_tracker.add_step(task_id, "extract", "Extracting content")
                progress_tracker.update_step(task_id, extract_step, TaskStatus.IN_PROGRESS)
                
                document_structure = processor.extract_content(file_item.file_path)
                total_items = len(document_structure.translatable_content)
                
                progress_tracker.update_task_progress(task_id, 0, TaskStatus.IN_PROGRESS)
                progress_tracker.update_step(task_id, extract_step, TaskStatus.COMPLETED, 1.0)
                
                # Translate content
                translate_step = progress_tracker.add_step(task_id, "translate", "Translating content")
                progress_tracker.update_step(task_id, translate_step, TaskStatus.IN_PROGRESS)
                
                translations = await self._translate_content_batch(
                    document_structure.translatable_content,
                    job.source_language,
                    job.target_language,
                    task_id
                )
                
                progress_tracker.update_step(task_id, translate_step, TaskStatus.COMPLETED, 1.0)
                
                # Reconstruct document
                reconstruct_step = progress_tracker.add_step(task_id, "reconstruct", "Reconstructing document")
                progress_tracker.update_step(task_id, reconstruct_step, TaskStatus.IN_PROGRESS)
                
                reconstructed_data = processor.reconstruct_document(document_structure, translations)
                
                progress_tracker.update_step(task_id, reconstruct_step, TaskStatus.COMPLETED, 1.0)
                progress_tracker.update_task_progress(task_id, total_items, TaskStatus.COMPLETED)
                
                # Save result
                output_path = self._save_translated_file(file_item, reconstructed_data, job)
                
                # Update file status
                file_item.status = BatchStatus.COMPLETED
                file_item.result_data = output_path
                file_item.metadata.update({
                    'total_items': total_items,
                    'output_path': output_path
                })
                
                # Update translation history
                translation_history.update_entry(
                    task_id,
                    status="completed",
                    total_items=total_items,
                    processed_items=total_items
                )
                
                logger.info(f"Successfully processed file {file_item.filename}")
                
            except Exception as e:
                # Handle file processing error
                file_item.status = BatchStatus.FAILED
                file_item.error_message = str(e)
                
                progress_tracker.update_task_progress(task_id, 0, TaskStatus.FAILED, str(e))
                translation_history.update_entry(task_id, status="failed", error_message=str(e))
                
                logger.error(f"Error processing file {file_item.filename}: {e}")
                
                with self.job_lock:
                    job.failed_files += 1
            
            # Update job progress
            with self.job_lock:
                if file_item.status == BatchStatus.COMPLETED:
                    job.processed_files += 1
                
                job.progress = (job.processed_files + job.failed_files) / job.total_files
            
            # Call progress callback if provided
            if progress_callback:
                try:
                    progress_callback(job)
                except Exception as e:
                    logger.error(f"Error in progress callback: {e}")
                    
        except Exception as e:
            logger.error(f"Unexpected error processing file {file_item.filename}: {e}")
            file_item.status = BatchStatus.FAILED
            file_item.error_message = str(e)
            
            with self.job_lock:
                job.failed_files += 1
    
    async def _translate_content_batch(self, content_list, source_language: str, 
                                     target_language: str, task_id: str) -> Dict[str, str]:
        """Translate content for batch processing"""
        from .translation_service import TranslationRequest
        
        if not content_list:
            return {}
        
        # Prepare translation requests
        translation_requests = []
        for content in content_list:
            translation_requests.append(TranslationRequest(
                text=content.content,
                source_language=source_language,
                target_language=target_language,
                location=content.location,
                metadata=content.metadata
            ))
        
        # Translate using the service
        translation_results = await self.translation_service.translate_batch(translation_requests)
        
        # Convert to dictionary
        translations = {}
        for result in translation_results:
            translations[result.location] = result.translated_text
            
            # Update progress
            current_progress = len(translations)
            total_items = len(content_list)
            progress_tracker.update_task_progress(task_id, current_progress)
        
        return translations
    
    def _save_translated_file(self, file_item: BatchFileItem, reconstructed_data: Any, 
                            job: BatchTranslationJob) -> str:
        """Save translated file to output directory"""
        try:
            # Create output directory
            output_dir = Path(__file__).parent.parent / "output" / job.batch_id
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate output filename
            name_parts = file_item.filename.rsplit('.', 1)
            if len(name_parts) == 2:
                base_name, extension = name_parts
                output_filename = f"{base_name}_translated_{job.target_language}.{extension}"
            else:
                output_filename = f"{file_item.filename}_translated_{job.target_language}"
            
            output_path = output_dir / output_filename
            
            # Save based on file type
            if file_item.file_type == '.xlsx' and isinstance(reconstructed_data, dict):
                # Save Excel file
                import pandas as pd
                with pd.ExcelWriter(str(output_path), engine='openpyxl') as writer:
                    for sheet_name, df in reconstructed_data.items():
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            elif file_item.file_type in ['.txt', '.csv']:
                # Save text file
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(str(reconstructed_data))
            
            else:
                # For other formats, save as JSON for now
                import json
                with open(output_path.with_suffix('.json'), 'w', encoding='utf-8') as f:
                    json.dump(reconstructed_data, f, indent=2, ensure_ascii=False)
                output_path = output_path.with_suffix('.json')
            
            logger.info(f"Saved translated file: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error saving translated file: {e}")
            raise
    
    def get_batch_status(self, batch_id: str) -> Optional[BatchTranslationJob]:
        """Get status of a batch job"""
        with self.job_lock:
            return self.active_jobs.get(batch_id)
    
    def cancel_batch_job(self, batch_id: str) -> bool:
        """Cancel a batch job"""
        with self.job_lock:
            if batch_id not in self.active_jobs:
                return False
            
            job = self.active_jobs[batch_id]
            if job.status not in [BatchStatus.PENDING, BatchStatus.PROCESSING]:
                return False
            
            job.status = BatchStatus.CANCELLED
            
            # Cancel individual file tasks
            for file_item in job.files:
                if file_item.task_id and file_item.status == BatchStatus.PROCESSING:
                    progress_tracker.cancel_task(file_item.task_id)
                    file_item.status = BatchStatus.CANCELLED
            
            logger.info(f"Cancelled batch job {batch_id}")
            return True
    
    def cleanup_completed_jobs(self, max_age_hours: int = 24):
        """Clean up old completed batch jobs"""
        import time
        current_time = time.time()
        cutoff_time = current_time - (max_age_hours * 3600)
        
        with self.job_lock:
            jobs_to_remove = []
            for batch_id, job in self.active_jobs.items():
                if (job.status in [BatchStatus.COMPLETED, BatchStatus.FAILED, BatchStatus.CANCELLED]
                    and job.completed_at and float(job.completed_at) < cutoff_time):
                    jobs_to_remove.append(batch_id)
            
            for batch_id in jobs_to_remove:
                del self.active_jobs[batch_id]
                logger.info(f"Cleaned up old batch job {batch_id}")
    
    def get_user_batch_jobs(self, user_id: str) -> List[BatchTranslationJob]:
        """Get all batch jobs for a user"""
        with self.job_lock:
            return [job for job in self.active_jobs.values() if job.user_id == user_id]


# Global batch processor instance
batch_processor = BatchProcessor()
