# Smart Translator Agent

An enhanced AI-powered translation system that improves upon the original eproexcella agent with better document support, advanced features, and improved user experience.

## Features

### Core Capabilities
- **Multi-format Support**: Excel (.xlsx), Word (.docx), PowerPoint (.pptx), Text (.txt), CSV (.csv)
- **Real-time Progress Tracking**: Monitor translation progress with detailed status updates
- **Translation Memory**: Cache and reuse translations for consistency and efficiency
- **Batch Processing**: Handle multiple files simultaneously
- **Enhanced Error Handling**: Comprehensive exception management and recovery
- **Advanced Document Processing**: Better content extraction and formatting preservation

### Advanced Features
- **Translation History**: Track all translation activities with detailed analytics
- **Progress Analytics**: Estimate completion times and track performance
- **Language Auto-detection**: Automatically detect source language
- **Column Selection**: Choose specific columns to translate in Excel files
- **Preview Mode**: Preview translations before full processing
- **Cancellation Support**: Cancel running translation tasks
- **Memory Management**: Automatic cleanup of old data

## Architecture

### Components

#### 1. SmartTranslatorAgent
The main orchestrator that coordinates all translation activities.

```python
from src.agents.smart_translator_agent.smart_translator import SmartTranslatorAgent

agent = SmartTranslatorAgent(
    bot_name="Smart Translator",
    user_id="user123",
    translation_provider=TranslationProvider.AZURE_TRANSLATOR,
    use_translation_memory=True,
    batch_size=100
)
```

#### 2. Document Processors
Handle different document formats and extract translatable content.

- **ExcelProcessor**: Handles .xlsx files with sheet and column support
- **TextProcessor**: Handles .txt and .csv files
- **DocumentProcessorFactory**: Creates appropriate processors for file types

#### 3. Translation Service
Manages translation providers and handles translation requests.

- **AzureTranslationService**: Microsoft Azure Translator integration
- **TranslationServiceFactory**: Creates translation service instances
- **Batch Translation**: Efficient processing of multiple translation requests

#### 4. Progress Tracker
Provides real-time progress monitoring and status updates.

```python
from src.agents.smart_translator_agent.models.progress_tracker import progress_tracker

# Create a task
task_id = progress_tracker.create_task("user123", "document.xlsx", 1000)

# Add steps
step_id = progress_tracker.add_step(task_id, "extract", "Extracting content")

# Update progress
progress_tracker.update_task_progress(task_id, processed_items=500)
```

#### 5. Translation Memory
Caches translations for consistency and performance.

```python
from src.agents.smart_translator_agent.models.translation_memory import translation_memory

# Store translation
translation_memory.store_translation(
    source_text="Hello",
    target_text="Hola",
    source_language="en",
    target_language="es",
    provider="azure"
)

# Retrieve translation
entry = translation_memory.get_translation("Hello", "en", "es")
```

#### 6. Translation History
Tracks all translation activities and provides analytics.

```python
from src.agents.smart_translator_agent.models.translation_history import translation_history

# Get user history
history = translation_history.get_user_history("user123", limit=50)

# Get statistics
stats = translation_history.get_user_statistics("user123", days=30)
```

#### 7. Batch Processor
Handles batch processing of multiple files.

```python
from src.agents.smart_translator_agent.models.batch_processor import batch_processor

# Create batch job
batch_id = batch_processor.create_batch_job(
    user_id="user123",
    file_paths=["file1.xlsx", "file2.txt"],
    source_language="en",
    target_language="es"
)

# Start processing
batch_processor.start_batch_job(batch_id)
```

## API Endpoints

### Single File Translation

#### Upload File
```
POST /translator/smart/api/upload
Content-Type: multipart/form-data

{
  "file": <file_data>
}
```

#### Translate Document
```
POST /translator/smart/api/translate
Content-Type: application/json

{
  "target_language": "es",
  "source_language": "auto",
  "selected_columns": ["Column1", "Column2"],
  "file_type": ".xlsx",
  "filename": "document.xlsx"
}
```

#### Preview Translation
```
POST /translator/smart/api/preview
Content-Type: application/json

{
  "translation_request": "Translate to Spanish",
  "target_language": "es",
  "source_language": "auto",
  "filename": "document.xlsx"
}
```

### Progress Tracking

#### Get Progress
```
GET /translator/smart/api/progress/{task_id}
```

#### Cancel Translation
```
POST /translator/smart/api/cancel/{task_id}
```

### Batch Processing

#### Upload Multiple Files
```
POST /translator/smart/api/batch/upload
Content-Type: multipart/form-data

{
  "files": [<file1>, <file2>, ...]
}
```

#### Start Batch Translation
```
POST /translator/smart/api/batch/translate
Content-Type: application/json

{
  "files": [
    {"file_path": "/path/to/file1.xlsx"},
    {"file_path": "/path/to/file2.txt"}
  ],
  "target_language": "es",
  "source_language": "auto"
}
```

#### Get Batch Status
```
GET /translator/smart/api/batch/status/{batch_id}
```

### History and Analytics

#### Get Translation History
```
GET /translator/smart/api/history?page=1&per_page=20
```

#### Get Supported Languages
```
GET /translator/smart/api/languages
```

## Configuration

### Environment Variables
```bash
# Azure Translator Configuration
TRANSLATOR_AZURE_AI_API_TRANSLATOR_ENDPOINT=https://api.cognitive.microsofttranslator.com
TRANSLATOR_AZURE_AI_API_TRANSLATOR_KEY=your_api_key
TRANSLATOR_AZURE_REGION=global

# Smart Translator Configuration
SMART_TRANSLATOR_BATCH_SIZE=100
SMART_TRANSLATOR_MAX_CONCURRENT=10
SMART_TRANSLATOR_USE_MEMORY=true
```

### Agent Configuration
```python
config = SmartTranslatorConfig(
    translation_provider=TranslationProvider.AZURE_TRANSLATOR,
    use_translation_memory=True,
    batch_size=100,
    max_concurrent_requests=10,
    enable_progress_tracking=True,
    auto_detect_language=True,
    confidence_threshold=0.5
)

agent = SmartTranslatorAgent("Smart Translator", user_id="user123", **config.__dict__)
```

## Usage Examples

### Basic Translation
```python
# Initialize agent
agent = SmartTranslatorAgent("Smart Translator", user_id="user123")

# Translate a document
answer = agent.ask(
    "Translate this Excel file to Spanish",
    file_path="/path/to/document.xlsx",
    target_language="es",
    source_language="auto"
)

print(f"Status: {answer.status}")
print(f"Data: {answer.data}")
```

### Batch Translation
```python
# Create batch job
batch_id = agent.create_batch_job(
    file_paths=["file1.xlsx", "file2.txt", "file3.docx"],
    source_language="en",
    target_language="es"
)

# Start processing
agent.start_batch_job(batch_id)

# Monitor progress
while True:
    status = agent.get_batch_status(batch_id)
    if status['status'] in ['completed', 'failed', 'cancelled']:
        break
    print(f"Progress: {status['progress']:.1%}")
    time.sleep(1)
```

### Translation History
```python
# Get recent translations
history = agent.get_translation_history(limit=10)

# Get statistics
stats = agent.get_translation_statistics(days=30)
print(f"Total translations: {stats['total_translations']}")
print(f"Success rate: {stats['success_rate']:.1%}")
```

## Testing

Run the test suite:
```bash
python -m pytest src/agents/smart_translator_agent/tests/
```

Run specific test:
```bash
python -m pytest src/agents/smart_translator_agent/tests/test_smart_translator.py::TestSmartTranslatorAgent::test_ask_method_success
```

## Performance Considerations

### Memory Management
- Translation memory automatically cleans up old entries
- Progress tracking tasks are cleaned up after completion
- Batch jobs are cleaned up after specified time

### Concurrency
- Batch processing supports configurable concurrency limits
- Translation requests are processed asynchronously
- Progress tracking is thread-safe

### Caching
- Translation memory provides intelligent caching
- Repeated translations are served from cache
- Cache hit rates improve over time

## Troubleshooting

### Common Issues

1. **File Upload Errors**
   - Check file format support
   - Verify file size limits
   - Ensure proper permissions

2. **Translation Failures**
   - Verify Azure Translator API credentials
   - Check network connectivity
   - Review error logs

3. **Progress Tracking Issues**
   - Ensure task IDs are valid
   - Check for cancelled tasks
   - Verify user permissions

### Logging
Enable debug logging:
```python
import logging
logging.getLogger('smart_translator_agent').setLevel(logging.DEBUG)
```

## Contributing

1. Follow the existing code structure
2. Add comprehensive tests for new features
3. Update documentation
4. Follow Python PEP 8 style guidelines
5. Add type hints where appropriate

## License

This project is part of the compli-bot system and follows the same licensing terms.
