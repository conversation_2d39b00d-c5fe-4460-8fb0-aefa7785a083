"""
Smart Translator Agent

Enhanced translation agent that improves upon eproexcella with better document support,
enhanced translation capabilities, improved error handling, and more flexible configuration.
"""

import os
import asyncio
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import pandas as pd
from pathlib import Path

from src.agents.agent import Agent
from src.agents.answer import Answer
from src.backend.contracts.chat_data import AgentName
from utils.core import get_logger
from config.config import EproExcelLaConfig

from .models.document_processor import DocumentProcessorFactory, DocumentStructure, TranslatableContent
from .models.translation_service import TranslationServiceFactory, TranslationProvider, TranslationRequest, TranslationResult
from .models.progress_tracker import progress_tracker, TaskStatus
from .models.translation_memory import translation_memory
from .models.translation_history import translation_history, TranslationHistoryEntry
from .models.batch_processor import batch_processor

logger = get_logger(__file__)


@dataclass
class SmartTranslatorConfig:
    """Configuration for Smart Translator Agent"""
    translation_provider: TranslationProvider = TranslationProvider.AZURE_TRANSLATOR
    use_translation_memory: bool = True
    batch_size: int = 100
    max_concurrent_requests: int = 10
    enable_progress_tracking: bool = True
    auto_detect_language: bool = True
    confidence_threshold: float = 0.5
    upload_directory: str = "upload"
    supported_formats: List[str] = None
    
    def __post_init__(self):
        if self.supported_formats is None:
            self.supported_formats = ['.xlsx', '.docx', '.pptx', '.txt', '.csv']


class SmartTranslatorAnswer(Answer):
    """Answer class for Smart Translator Agent"""
    
    def __init__(self, data: Any = None, task_id: str = None, status: str = "completed"):
        self.data = data
        self.task_id = task_id
        self.status = status
        self.info = ""
        self.request = ""
        self.agent_name = AgentName.SMART_TRANSLATOR.value if hasattr(AgentName, 'SMART_TRANSLATOR') else "smart_translator"
        self.metadata = {}
    
    def add_info_translation(self, info_translation: str) -> None:
        self.info = info_translation or ""
    
    def add_request(self, request: str) -> None:
        self.request = request
    
    def add_metadata(self, metadata: Dict[str, Any]) -> None:
        self.metadata.update(metadata)
    
    def to_json(self):
        return {
            "data": self.data,
            "task_id": self.task_id,
            "status": self.status,
            "info_translation": self.info,
            "request": self.request,
            "agent": self.agent_name,
            "metadata": self.metadata
        }


class SmartTranslatorAgent(Agent):
    """Enhanced translator agent with multi-format support and advanced features"""
    
    def __init__(self, bot_name: str, name: str = "smart_translator", user_id: str = "", **kwargs):
        """Initialize the Smart Translator Agent"""
        super().__init__(name, bot_name)
        
        self.user_id = user_id
        self.config = SmartTranslatorConfig(**kwargs)
        
        # Initialize services
        self.translation_service = TranslationServiceFactory.create_service(self.config.translation_provider)
        self.document_processor_factory = DocumentProcessorFactory()
        
        # Setup upload directory
        self.upload_dir = Path(__file__).parent / self.config.upload_directory
        self.upload_dir.mkdir(exist_ok=True)
        
        logger.info(f"Smart Translator Agent initialized for user {user_id}")
    
    def get_file_path(self, filename: str = None) -> str:
        """Get the file path for user's uploaded file"""
        if filename:
            return str(self.upload_dir / f"{self.user_id}_{filename}")
        return str(self.upload_dir / f"data_{self.user_id}.xlsx")
    
    def ask(self, request: str, history=None, **kwargs) -> SmartTranslatorAnswer:
        """
        Process translation request
        
        Args:
            request: Natural language translation request
            history: Conversation history (optional)
            **kwargs: Additional parameters like preview, file_path, etc.
        
        Returns:
            SmartTranslatorAnswer: Translation result
        """
        try:
            # Extract parameters
            preview = kwargs.get('preview', False)
            file_path = kwargs.get('file_path', self.get_file_path())
            target_language = kwargs.get('target_language', 'en')
            source_language = kwargs.get('source_language', 'auto')
            selected_columns = kwargs.get('selected_columns', [])
            
            logger.info(f"Processing translation request: {request}")
            logger.info(f"File path: {file_path}, Preview: {preview}")
            
            # Check if file exists
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            
            # Get document processor
            processor = self.document_processor_factory.get_processor(file_path)
            if not processor:
                raise ValueError(f"Unsupported file format: {file_path}")
            
            # Create progress tracking task if enabled
            task_id = None
            if self.config.enable_progress_tracking:
                filename = os.path.basename(file_path)
                task_id = progress_tracker.create_task(self.user_id, filename, 0)  # Will update total later
                
                # Add initial steps
                extract_step = progress_tracker.add_step(task_id, "extract", "Extracting content from document")
                translate_step = progress_tracker.add_step(task_id, "translate", "Translating content")
                reconstruct_step = progress_tracker.add_step(task_id, "reconstruct", "Reconstructing document")
            
            try:
                # Add to translation history
                history_entry = TranslationHistoryEntry(
                    user_id=self.user_id,
                    task_id=task_id or f"task_{hash(request)}",
                    filename=os.path.basename(file_path),
                    file_type=os.path.splitext(file_path)[1],
                    source_language=source_language,
                    target_language=target_language,
                    status="processing"
                )
                translation_history.add_entry(history_entry)

                # Step 1: Extract document content
                if task_id:
                    progress_tracker.update_step(task_id, extract_step, TaskStatus.IN_PROGRESS)
                
                document_structure = processor.extract_content(file_path)
                
                # Filter content if specific columns are requested (for Excel)
                if selected_columns and hasattr(processor, 'can_process') and processor.can_process(file_path) and file_path.endswith('.xlsx'):
                    document_structure = self._filter_excel_columns(document_structure, selected_columns)
                
                # Limit content for preview
                if preview:
                    document_structure = self._limit_content_for_preview(document_structure)
                
                total_items = len(document_structure.translatable_content)
                
                if task_id:
                    progress_tracker.update_task_progress(task_id, 0, TaskStatus.IN_PROGRESS)
                    progress_tracker.update_step(task_id, extract_step, TaskStatus.COMPLETED, 1.0)
                    progress_tracker.update_step(task_id, translate_step, TaskStatus.IN_PROGRESS)
                
                logger.info(f"Extracted {total_items} translatable items")
                
                # Step 2: Translate content
                translations = asyncio.run(self._translate_content(
                    document_structure.translatable_content,
                    source_language,
                    target_language,
                    task_id
                ))
                
                if task_id:
                    progress_tracker.update_step(task_id, translate_step, TaskStatus.COMPLETED, 1.0)
                    progress_tracker.update_step(task_id, reconstruct_step, TaskStatus.IN_PROGRESS)
                
                # Step 3: Reconstruct document
                reconstructed_data = processor.reconstruct_document(document_structure, translations)
                
                if task_id:
                    progress_tracker.update_step(task_id, reconstruct_step, TaskStatus.COMPLETED, 1.0)
                    progress_tracker.update_task_progress(task_id, total_items, TaskStatus.COMPLETED)

                # Update translation history
                translation_history.update_entry(
                    history_entry.task_id,
                    status="completed",
                    total_items=total_items,
                    processed_items=total_items,
                    end_time=translation_memory.datetime.now()
                )
                
                # Prepare response data
                response_data = self._prepare_response_data(reconstructed_data, document_structure.document_type)
                
                # Create answer
                answer = SmartTranslatorAnswer(
                    data=response_data,
                    task_id=task_id,
                    status="completed"
                )
                answer.add_info_translation(f"Translated {total_items} items")
                answer.add_request(request)
                answer.add_metadata({
                    "total_items": total_items,
                    "source_language": source_language,
                    "target_language": target_language,
                    "document_type": document_structure.document_type.value,
                    "preview": preview
                })
                
                logger.info(f"Translation completed successfully. Task ID: {task_id}")
                return answer
                
            except Exception as e:
                if task_id:
                    progress_tracker.update_task_progress(task_id, 0, TaskStatus.FAILED, str(e))

                # Update translation history with error
                translation_history.update_entry(
                    history_entry.task_id,
                    status="failed",
                    error_message=str(e),
                    end_time=translation_memory.datetime.now()
                )
                raise
                
        except Exception as e:
            logger.error(f"Translation error: {e}")
            
            # Create error response
            answer = SmartTranslatorAnswer(
                data=None,
                task_id=task_id,
                status="failed"
            )
            answer.add_info_translation(f"Translation failed: {str(e)}")
            answer.add_request(request)
            
            return answer
    
    def _filter_excel_columns(self, document_structure: DocumentStructure, selected_columns: List[str]) -> DocumentStructure:
        """Filter Excel content to only include selected columns"""
        if not selected_columns:
            return document_structure
        
        filtered_content = []
        for content in document_structure.translatable_content:
            if content.metadata and content.metadata.get('column') in selected_columns:
                filtered_content.append(content)
        
        document_structure.translatable_content = filtered_content
        return document_structure
    
    def _limit_content_for_preview(self, document_structure: DocumentStructure, max_items: int = 40) -> DocumentStructure:
        """Limit content for preview mode"""
        if len(document_structure.translatable_content) > max_items:
            document_structure.translatable_content = document_structure.translatable_content[:max_items]
        return document_structure
    
    async def _translate_content(self, content_list: List[TranslatableContent], 
                               source_language: str, target_language: str, 
                               task_id: str = None) -> Dict[str, str]:
        """Translate content using translation service and memory"""
        translations = {}
        
        if not content_list:
            return translations
        
        # Check translation memory first if enabled
        cached_translations = {}
        if self.config.use_translation_memory:
            cache_requests = [(content.content, source_language, target_language) for content in content_list]
            cached_results = translation_memory.get_batch_translations(cache_requests)
            
            for content in content_list:
                cache_key = f"{translation_memory._get_text_hash(content.content)}:{source_language}:{target_language}"
                if cache_key in cached_results:
                    cached_translations[content.location] = cached_results[cache_key].target_text
        
        # Prepare requests for items not in cache
        translation_requests = []
        for content in content_list:
            if content.location not in cached_translations:
                translation_requests.append(TranslationRequest(
                    text=content.content,
                    source_language=source_language,
                    target_language=target_language,
                    location=content.location,
                    metadata=content.metadata
                ))
        
        logger.info(f"Found {len(cached_translations)} cached translations, need to translate {len(translation_requests)} items")
        
        # Translate uncached items
        if translation_requests:
            translation_results = await self.translation_service.translate_batch(translation_requests)
            
            # Store new translations in memory
            if self.config.use_translation_memory:
                memory_entries = []
                for result in translation_results:
                    memory_entries.append(translation_memory.TranslationEntry(
                        source_text=result.original_text,
                        target_text=result.translated_text,
                        source_language=result.source_language,
                        target_language=result.target_language,
                        provider=result.provider,
                        confidence=result.confidence,
                        created_at=translation_memory.datetime.now(),
                        last_used=translation_memory.datetime.now()
                    ))
                translation_memory.store_batch_translations(memory_entries)
            
            # Add to translations dict
            for result in translation_results:
                translations[result.location] = result.translated_text
                
                # Update progress if tracking enabled
                if task_id:
                    current_progress = len(translations) + len(cached_translations)
                    total_items = len(content_list)
                    progress_tracker.update_task_progress(task_id, current_progress)
        
        # Combine cached and new translations
        translations.update(cached_translations)
        
        return translations
    
    def _prepare_response_data(self, reconstructed_data: Any, document_type) -> Any:
        """Prepare response data based on document type"""
        if document_type.value == 'xlsx' and isinstance(reconstructed_data, dict):
            # For Excel, convert to JSON format similar to eproexcella
            combined_df = pd.DataFrame()
            for sheet_name, df in reconstructed_data.items():
                if combined_df.empty:
                    combined_df = df
                else:
                    # For multiple sheets, you might want to handle this differently
                    combined_df = pd.concat([combined_df, df], ignore_index=True)
            
            return combined_df.to_json()
        
        return reconstructed_data
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported file formats"""
        return self.document_processor_factory.get_supported_formats()
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported languages"""
        return self.translation_service.get_supported_languages()
    
    def get_task_progress(self, task_id: str) -> Optional[Dict]:
        """Get progress for a specific task"""
        return progress_tracker.get_task_summary(task_id)
    
    def cancel_task(self, task_id: str):
        """Cancel a running translation task"""
        progress_tracker.cancel_task(task_id)

    def get_translation_history(self, limit: int = 50, offset: int = 0) -> List[Dict]:
        """Get translation history for the user"""
        entries = translation_history.get_user_history(self.user_id, limit, offset)
        return [entry.to_dict() for entry in entries]

    def get_translation_statistics(self, days: int = 30) -> Dict[str, Any]:
        """Get translation statistics for the user"""
        return translation_history.get_user_statistics(self.user_id, days)

    def create_batch_job(self, file_paths: List[str], source_language: str,
                        target_language: str, **kwargs) -> str:
        """Create a batch translation job"""
        return batch_processor.create_batch_job(
            user_id=self.user_id,
            file_paths=file_paths,
            source_language=source_language,
            target_language=target_language,
            **kwargs
        )

    def start_batch_job(self, batch_id: str) -> bool:
        """Start a batch translation job"""
        return batch_processor.start_batch_job(batch_id)

    def get_batch_status(self, batch_id: str) -> Optional[Dict]:
        """Get status of a batch translation job"""
        job = batch_processor.get_batch_status(batch_id)
        if job and job.user_id == self.user_id:
            return {
                'batch_id': job.batch_id,
                'status': job.status.value,
                'total_files': job.total_files,
                'processed_files': job.processed_files,
                'failed_files': job.failed_files,
                'progress': job.progress,
                'created_at': job.created_at,
                'started_at': job.started_at,
                'completed_at': job.completed_at,
                'error_message': job.error_message
            }
        return None

    def cancel_batch_job(self, batch_id: str) -> bool:
        """Cancel a batch translation job"""
        job = batch_processor.get_batch_status(batch_id)
        if job and job.user_id == self.user_id:
            return batch_processor.cancel_batch_job(batch_id)
        return False

    def get_user_batch_jobs(self) -> List[Dict]:
        """Get all batch jobs for the user"""
        jobs = batch_processor.get_user_batch_jobs(self.user_id)
        return [
            {
                'batch_id': job.batch_id,
                'status': job.status.value,
                'total_files': job.total_files,
                'processed_files': job.processed_files,
                'failed_files': job.failed_files,
                'progress': job.progress,
                'created_at': job.created_at,
                'started_at': job.started_at,
                'completed_at': job.completed_at
            }
            for job in jobs
        ]

    def get_translation_memory_stats(self) -> Dict[str, Any]:
        """Get translation memory statistics"""
        return translation_memory.get_statistics()

    def cleanup_old_data(self):
        """Clean up old translation data"""
        # Clean up old progress tracking tasks
        progress_tracker.cleanup_old_tasks()

        # Clean up old translation memory entries
        translation_memory.cleanup_old_entries()

        # Clean up old translation history
        translation_history.cleanup_old_entries()

        # Clean up old batch jobs
        batch_processor.cleanup_completed_jobs()

        logger.info(f"Cleaned up old data for user {self.user_id}")
