"""
Translation Memory for Smart Translator Agent

Caches and manages translation results for efficiency and consistency.
"""

import hashlib
import json
import sqlite3
import threading
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import os
from pathlib import Path

from utils.core import get_logger, Singleton

logger = get_logger(__file__)


@dataclass
class TranslationEntry:
    """Represents a cached translation entry"""
    source_text: str
    target_text: str
    source_language: str
    target_language: str
    provider: str
    confidence: float
    created_at: datetime
    last_used: datetime
    usage_count: int = 1
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        data['last_used'] = self.last_used.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'TranslationEntry':
        """Create from dictionary"""
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        data['last_used'] = datetime.fromisoformat(data['last_used'])
        return cls(**data)


class TranslationMemory(metaclass=Singleton):
    """Manages translation cache and memory"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or self._get_default_db_path()
        self._lock = threading.Lock()
        self._init_database()
    
    def _get_default_db_path(self) -> str:
        """Get default database path"""
        # Create cache directory if it doesn't exist
        cache_dir = Path(__file__).parent.parent / "cache"
        cache_dir.mkdir(exist_ok=True)
        return str(cache_dir / "translation_memory.db")
    
    def _init_database(self):
        """Initialize SQLite database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS translations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        text_hash TEXT NOT NULL,
                        source_text TEXT NOT NULL,
                        target_text TEXT NOT NULL,
                        source_language TEXT NOT NULL,
                        target_language TEXT NOT NULL,
                        provider TEXT NOT NULL,
                        confidence REAL NOT NULL,
                        created_at TEXT NOT NULL,
                        last_used TEXT NOT NULL,
                        usage_count INTEGER DEFAULT 1,
                        UNIQUE(text_hash, source_language, target_language, provider)
                    )
                """)
                
                # Create indexes for better performance
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_text_hash 
                    ON translations(text_hash, source_language, target_language)
                """)
                
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_last_used 
                    ON translations(last_used)
                """)
                
                conn.commit()
                logger.info(f"Translation memory database initialized at {self.db_path}")
                
        except Exception as e:
            logger.error(f"Error initializing translation memory database: {e}")
            raise
    
    def _get_text_hash(self, text: str) -> str:
        """Generate hash for text content"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()
    
    def get_translation(self, source_text: str, source_language: str, 
                       target_language: str, provider: str = None) -> Optional[TranslationEntry]:
        """Get cached translation if available"""
        text_hash = self._get_text_hash(source_text)
        
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    conn.row_factory = sqlite3.Row
                    
                    query = """
                        SELECT * FROM translations 
                        WHERE text_hash = ? AND source_language = ? AND target_language = ?
                    """
                    params = [text_hash, source_language, target_language]
                    
                    if provider:
                        query += " AND provider = ?"
                        params.append(provider)
                    
                    query += " ORDER BY confidence DESC, usage_count DESC LIMIT 1"
                    
                    cursor = conn.execute(query, params)
                    row = cursor.fetchone()
                    
                    if row:
                        # Update usage statistics
                        now = datetime.now().isoformat()
                        conn.execute("""
                            UPDATE translations 
                            SET last_used = ?, usage_count = usage_count + 1
                            WHERE id = ?
                        """, [now, row['id']])
                        conn.commit()
                        
                        # Convert row to TranslationEntry
                        return TranslationEntry(
                            source_text=row['source_text'],
                            target_text=row['target_text'],
                            source_language=row['source_language'],
                            target_language=row['target_language'],
                            provider=row['provider'],
                            confidence=row['confidence'],
                            created_at=datetime.fromisoformat(row['created_at']),
                            last_used=datetime.fromisoformat(row['last_used']),
                            usage_count=row['usage_count']
                        )
            
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving translation from memory: {e}")
            return None
    
    def store_translation(self, source_text: str, target_text: str, 
                         source_language: str, target_language: str,
                         provider: str, confidence: float = 1.0):
        """Store translation in memory"""
        text_hash = self._get_text_hash(source_text)
        now = datetime.now().isoformat()
        
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    # Try to insert, or update if exists
                    conn.execute("""
                        INSERT OR REPLACE INTO translations 
                        (text_hash, source_text, target_text, source_language, 
                         target_language, provider, confidence, created_at, last_used, usage_count)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
                    """, [
                        text_hash, source_text, target_text, source_language,
                        target_language, provider, confidence, now, now
                    ])
                    conn.commit()
                    
        except Exception as e:
            logger.error(f"Error storing translation in memory: {e}")
    
    def get_batch_translations(self, requests: List[Tuple[str, str, str]]) -> Dict[str, TranslationEntry]:
        """Get multiple translations at once
        
        Args:
            requests: List of (source_text, source_language, target_language) tuples
            
        Returns:
            Dictionary mapping request key to TranslationEntry
        """
        results = {}
        
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    conn.row_factory = sqlite3.Row
                    
                    for source_text, source_language, target_language in requests:
                        text_hash = self._get_text_hash(source_text)
                        request_key = f"{text_hash}:{source_language}:{target_language}"
                        
                        cursor = conn.execute("""
                            SELECT * FROM translations 
                            WHERE text_hash = ? AND source_language = ? AND target_language = ?
                            ORDER BY confidence DESC, usage_count DESC LIMIT 1
                        """, [text_hash, source_language, target_language])
                        
                        row = cursor.fetchone()
                        if row:
                            results[request_key] = TranslationEntry(
                                source_text=row['source_text'],
                                target_text=row['target_text'],
                                source_language=row['source_language'],
                                target_language=row['target_language'],
                                provider=row['provider'],
                                confidence=row['confidence'],
                                created_at=datetime.fromisoformat(row['created_at']),
                                last_used=datetime.fromisoformat(row['last_used']),
                                usage_count=row['usage_count']
                            )
                    
                    # Update usage statistics for found entries
                    if results:
                        now = datetime.now().isoformat()
                        for entry in results.values():
                            conn.execute("""
                                UPDATE translations 
                                SET last_used = ?, usage_count = usage_count + 1
                                WHERE text_hash = ? AND source_language = ? AND target_language = ?
                            """, [
                                now, 
                                self._get_text_hash(entry.source_text),
                                entry.source_language,
                                entry.target_language
                            ])
                        conn.commit()
            
            return results
            
        except Exception as e:
            logger.error(f"Error retrieving batch translations from memory: {e}")
            return {}
    
    def store_batch_translations(self, translations: List[TranslationEntry]):
        """Store multiple translations at once"""
        if not translations:
            return
        
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    now = datetime.now().isoformat()
                    
                    for entry in translations:
                        text_hash = self._get_text_hash(entry.source_text)
                        
                        conn.execute("""
                            INSERT OR REPLACE INTO translations 
                            (text_hash, source_text, target_text, source_language, 
                             target_language, provider, confidence, created_at, last_used, usage_count)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
                        """, [
                            text_hash, entry.source_text, entry.target_text,
                            entry.source_language, entry.target_language,
                            entry.provider, entry.confidence, now, now
                        ])
                    
                    conn.commit()
                    logger.info(f"Stored {len(translations)} translations in memory")
                    
        except Exception as e:
            logger.error(f"Error storing batch translations in memory: {e}")
    
    def cleanup_old_entries(self, max_age_days: int = 30, max_entries: int = 10000):
        """Clean up old or excess translation entries"""
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    # Remove entries older than max_age_days
                    cutoff_date = (datetime.now() - timedelta(days=max_age_days)).isoformat()
                    
                    cursor = conn.execute("""
                        DELETE FROM translations 
                        WHERE last_used < ? AND usage_count = 1
                    """, [cutoff_date])
                    
                    deleted_old = cursor.rowcount
                    
                    # Keep only the most recent max_entries
                    conn.execute("""
                        DELETE FROM translations 
                        WHERE id NOT IN (
                            SELECT id FROM translations 
                            ORDER BY last_used DESC, usage_count DESC 
                            LIMIT ?
                        )
                    """, [max_entries])
                    
                    deleted_excess = cursor.rowcount - deleted_old
                    
                    conn.commit()
                    
                    if deleted_old > 0 or deleted_excess > 0:
                        logger.info(f"Cleaned up translation memory: {deleted_old} old entries, {deleted_excess} excess entries")
                    
        except Exception as e:
            logger.error(f"Error cleaning up translation memory: {e}")
    
    def get_statistics(self) -> Dict:
        """Get translation memory statistics"""
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute("SELECT COUNT(*) as total FROM translations")
                    total = cursor.fetchone()[0]
                    
                    cursor = conn.execute("""
                        SELECT source_language, target_language, COUNT(*) as count
                        FROM translations 
                        GROUP BY source_language, target_language
                        ORDER BY count DESC
                    """)
                    language_pairs = cursor.fetchall()
                    
                    cursor = conn.execute("""
                        SELECT provider, COUNT(*) as count
                        FROM translations 
                        GROUP BY provider
                        ORDER BY count DESC
                    """)
                    providers = cursor.fetchall()
                    
                    return {
                        "total_entries": total,
                        "language_pairs": [{"from": row[0], "to": row[1], "count": row[2]} for row in language_pairs],
                        "providers": [{"provider": row[0], "count": row[1]} for row in providers]
                    }
                    
        except Exception as e:
            logger.error(f"Error getting translation memory statistics: {e}")
            return {"total_entries": 0, "language_pairs": [], "providers": []}


# Global translation memory instance
translation_memory = TranslationMemory()
