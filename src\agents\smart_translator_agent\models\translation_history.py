"""
Translation History for Smart Translator Agent

Manages translation history and provides analytics for translation tasks.
"""

import sqlite3
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path
import threading

from utils.core import get_logger, Singleton

logger = get_logger(__file__)


@dataclass
class TranslationHistoryEntry:
    """Represents a translation history entry"""
    id: Optional[int] = None
    user_id: str = ""
    task_id: str = ""
    filename: str = ""
    file_type: str = ""
    source_language: str = ""
    target_language: str = ""
    total_items: int = 0
    processed_items: int = 0
    status: str = ""
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    created_at: Optional[datetime] = None
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        if self.start_time:
            data['start_time'] = self.start_time.isoformat()
        if self.end_time:
            data['end_time'] = self.end_time.isoformat()
        if self.created_at:
            data['created_at'] = self.created_at.isoformat()
        if self.metadata:
            data['metadata'] = json.dumps(self.metadata)
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'TranslationHistoryEntry':
        """Create from dictionary"""
        if 'start_time' in data and data['start_time']:
            data['start_time'] = datetime.fromisoformat(data['start_time'])
        if 'end_time' in data and data['end_time']:
            data['end_time'] = datetime.fromisoformat(data['end_time'])
        if 'created_at' in data and data['created_at']:
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        if 'metadata' in data and data['metadata']:
            try:
                data['metadata'] = json.loads(data['metadata'])
            except (json.JSONDecodeError, TypeError):
                data['metadata'] = {}
        return cls(**data)


class TranslationHistory(metaclass=Singleton):
    """Manages translation history and analytics"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or self._get_default_db_path()
        self._lock = threading.Lock()
        self._init_database()
    
    def _get_default_db_path(self) -> str:
        """Get default database path"""
        cache_dir = Path(__file__).parent.parent / "cache"
        cache_dir.mkdir(exist_ok=True)
        return str(cache_dir / "translation_history.db")
    
    def _init_database(self):
        """Initialize SQLite database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS translation_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id TEXT NOT NULL,
                        task_id TEXT NOT NULL UNIQUE,
                        filename TEXT NOT NULL,
                        file_type TEXT NOT NULL,
                        source_language TEXT NOT NULL,
                        target_language TEXT NOT NULL,
                        total_items INTEGER DEFAULT 0,
                        processed_items INTEGER DEFAULT 0,
                        status TEXT NOT NULL,
                        start_time TEXT,
                        end_time TEXT,
                        duration_seconds REAL,
                        error_message TEXT,
                        metadata TEXT,
                        created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # Create indexes for better performance
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_user_id 
                    ON translation_history(user_id)
                """)
                
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_task_id 
                    ON translation_history(task_id)
                """)
                
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_created_at 
                    ON translation_history(created_at)
                """)
                
                conn.commit()
                logger.info(f"Translation history database initialized at {self.db_path}")
                
        except Exception as e:
            logger.error(f"Error initializing translation history database: {e}")
            raise
    
    def add_entry(self, entry: TranslationHistoryEntry) -> int:
        """Add a new translation history entry"""
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    if not entry.created_at:
                        entry.created_at = datetime.now()
                    
                    cursor = conn.execute("""
                        INSERT INTO translation_history 
                        (user_id, task_id, filename, file_type, source_language, target_language,
                         total_items, processed_items, status, start_time, end_time, 
                         duration_seconds, error_message, metadata, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, [
                        entry.user_id, entry.task_id, entry.filename, entry.file_type,
                        entry.source_language, entry.target_language, entry.total_items,
                        entry.processed_items, entry.status,
                        entry.start_time.isoformat() if entry.start_time else None,
                        entry.end_time.isoformat() if entry.end_time else None,
                        entry.duration_seconds, entry.error_message,
                        json.dumps(entry.metadata) if entry.metadata else None,
                        entry.created_at.isoformat()
                    ])
                    
                    entry_id = cursor.lastrowid
                    conn.commit()
                    
                    logger.info(f"Added translation history entry {entry_id} for task {entry.task_id}")
                    return entry_id
                    
        except Exception as e:
            logger.error(f"Error adding translation history entry: {e}")
            raise
    
    def update_entry(self, task_id: str, **updates) -> bool:
        """Update an existing translation history entry"""
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    # Build update query dynamically
                    set_clauses = []
                    values = []
                    
                    for key, value in updates.items():
                        if key in ['start_time', 'end_time'] and isinstance(value, datetime):
                            value = value.isoformat()
                        elif key == 'metadata' and isinstance(value, dict):
                            value = json.dumps(value)
                        
                        set_clauses.append(f"{key} = ?")
                        values.append(value)
                    
                    if not set_clauses:
                        return False
                    
                    values.append(task_id)
                    
                    cursor = conn.execute(f"""
                        UPDATE translation_history 
                        SET {', '.join(set_clauses)}
                        WHERE task_id = ?
                    """, values)
                    
                    updated = cursor.rowcount > 0
                    conn.commit()
                    
                    if updated:
                        logger.info(f"Updated translation history for task {task_id}")
                    
                    return updated
                    
        except Exception as e:
            logger.error(f"Error updating translation history entry: {e}")
            return False
    
    def get_entry_by_task_id(self, task_id: str) -> Optional[TranslationHistoryEntry]:
        """Get translation history entry by task ID"""
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    conn.row_factory = sqlite3.Row
                    
                    cursor = conn.execute("""
                        SELECT * FROM translation_history 
                        WHERE task_id = ?
                    """, [task_id])
                    
                    row = cursor.fetchone()
                    if row:
                        return self._row_to_entry(row)
                    
                    return None
                    
        except Exception as e:
            logger.error(f"Error getting translation history entry: {e}")
            return None
    
    def get_user_history(self, user_id: str, limit: int = 50, offset: int = 0) -> List[TranslationHistoryEntry]:
        """Get translation history for a specific user"""
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    conn.row_factory = sqlite3.Row
                    
                    cursor = conn.execute("""
                        SELECT * FROM translation_history 
                        WHERE user_id = ?
                        ORDER BY created_at DESC
                        LIMIT ? OFFSET ?
                    """, [user_id, limit, offset])
                    
                    rows = cursor.fetchall()
                    return [self._row_to_entry(row) for row in rows]
                    
        except Exception as e:
            logger.error(f"Error getting user translation history: {e}")
            return []
    
    def get_user_statistics(self, user_id: str, days: int = 30) -> Dict[str, Any]:
        """Get translation statistics for a user"""
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
                    
                    # Total translations
                    cursor = conn.execute("""
                        SELECT COUNT(*) as total_translations,
                               SUM(total_items) as total_items_translated,
                               AVG(duration_seconds) as avg_duration
                        FROM translation_history 
                        WHERE user_id = ? AND created_at >= ?
                    """, [user_id, cutoff_date])
                    
                    stats = cursor.fetchone()
                    
                    # Language pairs
                    cursor = conn.execute("""
                        SELECT source_language, target_language, COUNT(*) as count
                        FROM translation_history 
                        WHERE user_id = ? AND created_at >= ?
                        GROUP BY source_language, target_language
                        ORDER BY count DESC
                        LIMIT 10
                    """, [user_id, cutoff_date])
                    
                    language_pairs = cursor.fetchall()
                    
                    # File types
                    cursor = conn.execute("""
                        SELECT file_type, COUNT(*) as count
                        FROM translation_history 
                        WHERE user_id = ? AND created_at >= ?
                        GROUP BY file_type
                        ORDER BY count DESC
                    """, [user_id, cutoff_date])
                    
                    file_types = cursor.fetchall()
                    
                    # Success rate
                    cursor = conn.execute("""
                        SELECT status, COUNT(*) as count
                        FROM translation_history 
                        WHERE user_id = ? AND created_at >= ?
                        GROUP BY status
                    """, [user_id, cutoff_date])
                    
                    status_counts = cursor.fetchall()
                    
                    return {
                        "total_translations": stats[0] or 0,
                        "total_items_translated": stats[1] or 0,
                        "average_duration_seconds": stats[2] or 0,
                        "language_pairs": [
                            {"from": row[0], "to": row[1], "count": row[2]} 
                            for row in language_pairs
                        ],
                        "file_types": [
                            {"type": row[0], "count": row[1]} 
                            for row in file_types
                        ],
                        "status_distribution": [
                            {"status": row[0], "count": row[1]} 
                            for row in status_counts
                        ],
                        "period_days": days
                    }
                    
        except Exception as e:
            logger.error(f"Error getting user statistics: {e}")
            return {}
    
    def _row_to_entry(self, row) -> TranslationHistoryEntry:
        """Convert database row to TranslationHistoryEntry"""
        data = dict(row)
        
        # Convert datetime strings back to datetime objects
        for field in ['start_time', 'end_time', 'created_at']:
            if data.get(field):
                try:
                    data[field] = datetime.fromisoformat(data[field])
                except ValueError:
                    data[field] = None
        
        # Parse metadata JSON
        if data.get('metadata'):
            try:
                data['metadata'] = json.loads(data['metadata'])
            except (json.JSONDecodeError, TypeError):
                data['metadata'] = {}
        
        return TranslationHistoryEntry(**data)
    
    def cleanup_old_entries(self, max_age_days: int = 90):
        """Clean up old translation history entries"""
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    cutoff_date = (datetime.now() - timedelta(days=max_age_days)).isoformat()
                    
                    cursor = conn.execute("""
                        DELETE FROM translation_history 
                        WHERE created_at < ?
                    """, [cutoff_date])
                    
                    deleted_count = cursor.rowcount
                    conn.commit()
                    
                    if deleted_count > 0:
                        logger.info(f"Cleaned up {deleted_count} old translation history entries")
                    
                    return deleted_count
                    
        except Exception as e:
            logger.error(f"Error cleaning up translation history: {e}")
            return 0


# Global translation history instance
translation_history = TranslationHistory()
