"""
Progress Tracker for Smart Translator Agent

Tracks and manages translation progress for real-time feedback.
"""

import time
import threading
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import uuid

from utils.core import get_logger

logger = get_logger(__file__)


class TaskStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ProgressStep:
    """Represents a single step in the translation process"""
    step_id: str
    name: str
    description: str
    status: TaskStatus = TaskStatus.PENDING
    progress: float = 0.0  # 0.0 to 1.0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None
    metadata: Dict = field(default_factory=dict)


@dataclass
class TranslationProgress:
    """Represents the overall progress of a translation task"""
    task_id: str
    user_id: str
    file_name: str
    total_items: int
    processed_items: int = 0
    status: TaskStatus = TaskStatus.PENDING
    steps: List[ProgressStep] = field(default_factory=list)
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None
    error_message: Optional[str] = None
    result_data: Optional[Dict] = None
    
    @property
    def overall_progress(self) -> float:
        """Calculate overall progress as percentage"""
        if self.total_items == 0:
            return 0.0
        return min(self.processed_items / self.total_items, 1.0)
    
    @property
    def elapsed_time(self) -> Optional[timedelta]:
        """Get elapsed time since start"""
        if self.start_time:
            end = self.end_time or datetime.now()
            return end - self.start_time
        return None
    
    @property
    def estimated_time_remaining(self) -> Optional[timedelta]:
        """Estimate remaining time based on current progress"""
        if self.start_time and self.overall_progress > 0:
            elapsed = self.elapsed_time
            if elapsed:
                total_estimated = elapsed / self.overall_progress
                return total_estimated - elapsed
        return None


class ProgressTracker:
    """Manages progress tracking for translation tasks"""
    
    def __init__(self):
        self._tasks: Dict[str, TranslationProgress] = {}
        self._callbacks: Dict[str, List[Callable]] = {}
        self._lock = threading.Lock()
    
    def create_task(self, user_id: str, file_name: str, total_items: int) -> str:
        """Create a new translation task and return task ID"""
        task_id = str(uuid.uuid4())
        
        with self._lock:
            self._tasks[task_id] = TranslationProgress(
                task_id=task_id,
                user_id=user_id,
                file_name=file_name,
                total_items=total_items,
                start_time=datetime.now()
            )
            self._callbacks[task_id] = []
        
        logger.info(f"Created translation task {task_id} for user {user_id}")
        return task_id
    
    def add_step(self, task_id: str, step_name: str, step_description: str) -> str:
        """Add a new step to the task"""
        step_id = str(uuid.uuid4())
        
        with self._lock:
            if task_id in self._tasks:
                step = ProgressStep(
                    step_id=step_id,
                    name=step_name,
                    description=step_description
                )
                self._tasks[task_id].steps.append(step)
                self._notify_callbacks(task_id)
        
        return step_id
    
    def update_step(self, task_id: str, step_id: str, status: TaskStatus = None, 
                   progress: float = None, error_message: str = None):
        """Update a specific step"""
        with self._lock:
            if task_id in self._tasks:
                task = self._tasks[task_id]
                for step in task.steps:
                    if step.step_id == step_id:
                        if status is not None:
                            step.status = status
                            if status == TaskStatus.IN_PROGRESS and step.start_time is None:
                                step.start_time = datetime.now()
                            elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                                step.end_time = datetime.now()
                        
                        if progress is not None:
                            step.progress = max(0.0, min(1.0, progress))
                        
                        if error_message is not None:
                            step.error_message = error_message
                        
                        self._notify_callbacks(task_id)
                        break
    
    def update_task_progress(self, task_id: str, processed_items: int = None, 
                           status: TaskStatus = None, error_message: str = None,
                           result_data: Dict = None):
        """Update overall task progress"""
        with self._lock:
            if task_id in self._tasks:
                task = self._tasks[task_id]
                
                if processed_items is not None:
                    task.processed_items = processed_items
                
                if status is not None:
                    task.status = status
                    if status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                        task.end_time = datetime.now()
                
                if error_message is not None:
                    task.error_message = error_message
                
                if result_data is not None:
                    task.result_data = result_data
                
                # Update estimated completion time
                if task.overall_progress > 0 and task.status == TaskStatus.IN_PROGRESS:
                    remaining_time = task.estimated_time_remaining
                    if remaining_time:
                        task.estimated_completion = datetime.now() + remaining_time
                
                self._notify_callbacks(task_id)
    
    def get_task_progress(self, task_id: str) -> Optional[TranslationProgress]:
        """Get current progress for a task"""
        with self._lock:
            return self._tasks.get(task_id)
    
    def get_user_tasks(self, user_id: str) -> List[TranslationProgress]:
        """Get all tasks for a specific user"""
        with self._lock:
            return [task for task in self._tasks.values() if task.user_id == user_id]
    
    def add_callback(self, task_id: str, callback: Callable[[TranslationProgress], None]):
        """Add a callback to be notified of progress updates"""
        with self._lock:
            if task_id in self._callbacks:
                self._callbacks[task_id].append(callback)
    
    def remove_callback(self, task_id: str, callback: Callable):
        """Remove a progress callback"""
        with self._lock:
            if task_id in self._callbacks:
                try:
                    self._callbacks[task_id].remove(callback)
                except ValueError:
                    pass
    
    def _notify_callbacks(self, task_id: str):
        """Notify all callbacks for a task (called with lock held)"""
        if task_id in self._callbacks and task_id in self._tasks:
            task = self._tasks[task_id]
            for callback in self._callbacks[task_id]:
                try:
                    callback(task)
                except Exception as e:
                    logger.error(f"Error in progress callback: {e}")
    
    def cleanup_old_tasks(self, max_age_hours: int = 24):
        """Clean up old completed tasks"""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        with self._lock:
            tasks_to_remove = []
            for task_id, task in self._tasks.items():
                if (task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] 
                    and task.end_time and task.end_time < cutoff_time):
                    tasks_to_remove.append(task_id)
            
            for task_id in tasks_to_remove:
                del self._tasks[task_id]
                if task_id in self._callbacks:
                    del self._callbacks[task_id]
                logger.info(f"Cleaned up old task {task_id}")
    
    def cancel_task(self, task_id: str):
        """Cancel a running task"""
        with self._lock:
            if task_id in self._tasks:
                task = self._tasks[task_id]
                if task.status in [TaskStatus.PENDING, TaskStatus.IN_PROGRESS]:
                    task.status = TaskStatus.CANCELLED
                    task.end_time = datetime.now()
                    
                    # Cancel all pending steps
                    for step in task.steps:
                        if step.status in [TaskStatus.PENDING, TaskStatus.IN_PROGRESS]:
                            step.status = TaskStatus.CANCELLED
                            step.end_time = datetime.now()
                    
                    self._notify_callbacks(task_id)
                    logger.info(f"Cancelled task {task_id}")
    
    def get_task_summary(self, task_id: str) -> Optional[Dict]:
        """Get a summary of task progress suitable for API responses"""
        task = self.get_task_progress(task_id)
        if not task:
            return None
        
        return {
            "task_id": task.task_id,
            "status": task.status.value,
            "progress": task.overall_progress,
            "processed_items": task.processed_items,
            "total_items": task.total_items,
            "file_name": task.file_name,
            "start_time": task.start_time.isoformat() if task.start_time else None,
            "end_time": task.end_time.isoformat() if task.end_time else None,
            "estimated_completion": task.estimated_completion.isoformat() if task.estimated_completion else None,
            "elapsed_time": str(task.elapsed_time) if task.elapsed_time else None,
            "estimated_time_remaining": str(task.estimated_time_remaining) if task.estimated_time_remaining else None,
            "error_message": task.error_message,
            "steps": [
                {
                    "name": step.name,
                    "description": step.description,
                    "status": step.status.value,
                    "progress": step.progress,
                    "error_message": step.error_message
                }
                for step in task.steps
            ]
        }


# Global progress tracker instance
progress_tracker = ProgressTracker()
