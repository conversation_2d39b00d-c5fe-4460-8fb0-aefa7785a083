from flask import Blueprint
from .translation_routes import translator_bot_routes
from .smart_translation_routes import smart_translation_routes

# Create the main blueprint for translation tool
translator_bot = Blueprint('translator_bot', __name__, template_folder='templates')

# Register the sub-blueprints
translator_bot.register_blueprint(translator_bot_routes)
translator_bot.register_blueprint(smart_translation_routes, url_prefix='/smart')